import os
import fnmatch
from pathlib import Path

import sys
from typing import List

sys.path.insert(0, str(Path(__file__).parent.parent.parent))
from codecompass.llm.openai_provider import get_openai_client
from codecompass.config import IgnoreConfig


def get_project_structure(repo_path, include: List[str] = None, exclude: List[str] = None, file_insights: dict = None):
    """Generate a tree-like project structure representation.

    Args:
        repo_path: Root path of the repository
        include: Include path patterns, if not provided, include all files
        exclude: Exclude path patterns, if not provided, use empty list
        file_insights: Dict mapping file paths to their code insights

    Returns:
        str: Formatted string showing directory structure
    """
    if include is None:
        include = []
    if exclude is None:
        exclude = []
    if file_insights is None:
        file_insights = {}
    
    def should_include_file(file_name, file_path):
        """检查文件是否应该被包含（根据include/exclude模式）"""
        # 构建相对于repo_path的完整路径用于匹配
        relative_path = os.path.relpath(file_path, repo_path)
        
        # 处理include逻辑
        if include:
            # 如果有include模式，只保留匹配的文件
            include_match = False
            for pattern in include:
                if fnmatch.fnmatch(file_name, pattern) or fnmatch.fnmatch(relative_path, pattern):
                    include_match = True
                    break
            if not include_match:
                return False  # 不匹配include模式，排除
        
        # 处理exclude逻辑
        for pattern in exclude:
            if fnmatch.fnmatch(file_name, pattern) or fnmatch.fnmatch(relative_path, pattern):
                return False  # 匹配exclude模式，排除
        
        return True  # 通过所有过滤条件
    
    output = []
    for root, _, files in os.walk(repo_path):
        level = root.replace(repo_path, "").count(os.sep)
        indent = "│   " * level
        
        # 检查目录是否应该被排除
        if any(fnmatch.fnmatch(root, pattern) for pattern in exclude):
            continue

        # Add directory name
        basename = os.path.basename(root)
        if level == 0:
            output.append(f"{basename}/")
        else:
            output.append(f"{indent[:-4]}├── {basename}/")

        # 过滤文件
        filtered_files = []
        for file in files:
            file_path = os.path.join(root, file)
            if should_include_file(file, file_path):
                filtered_files.append(file)

        # Add files
        for file in sorted(filtered_files):
            file_path = os.path.join(root, file)
            relative_path = os.path.relpath(file_path, repo_path)
            
            # 查找对应的code insight
            file_line = f"{indent}├── {file}"
            if relative_path in file_insights and file_insights[relative_path]:
                # 合并所有insights
                insights = file_insights[relative_path]
                combined_insight = '; '.join(insights)
                file_line = f"{indent}├── {file} ({combined_insight})"
            
            output.append(file_line)

    return "\n".join(output)