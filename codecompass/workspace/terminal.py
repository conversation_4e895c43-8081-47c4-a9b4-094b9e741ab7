import os
import subprocess
import sys
from typing import Optional, Dict, List

from codecompass.utils.file_oper import read_file, READLINES

def get_current_directory() -> str:
    """Get the current working directory."""
    return os.getcwd()

def get_environment_variables() -> Dict[str, str]:
    """Get current environment variables."""
    return dict(os.environ)

def get_shell_info() -> Dict[str, Optional[str]]:
    """Get information about the current shell."""
    return {
        'shell': os.environ.get('SHELL'),
        'term': os.environ.get('TERM'),
        'user': os.environ.get('USER'),
        'home': os.environ.get('HOME')
    }

def get_recent_commands(limit: int = 10) -> List[str]:
    """Get recent command history (bash/zsh)."""
    try:
        shell = os.environ.get('SHELL', '')
        if 'bash' in shell:
            history_file = os.path.expanduser('~/.bash_history')
        elif 'zsh' in shell:
            history_file = os.path.expanduser('~/.zsh_history')
        else:
            return []
        
        if os.path.exists(history_file):
            lines = read_file(history_file, oper = READLINES)
            return [line.strip() for line in lines[-limit:] if line.strip()]
    except Exception as e:
        print(f"Error reading command history: {e}", file=sys.stderr)
    return []

def get_running_processes() -> List[str]:
    """Get list of running processes."""
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        if result.returncode == 0:
            return result.stdout.split('\n')[:20]  # Limit to first 20 processes
    except Exception as e:
        print(f"Error getting processes: {e}", file=sys.stderr)
    return []

def get_terminal_context() -> Dict:
    """Get comprehensive terminal context information."""
    return {
        'current_directory': get_current_directory(),
        'shell_info': get_shell_info(),
        'recent_commands': get_recent_commands(),
        'environment_vars': {k: v for k, v in get_environment_variables().items() 
                           if k in ['PATH', 'PYTHONPATH', 'VIRTUAL_ENV', 'CONDA_DEFAULT_ENV']},
        'running_processes': get_running_processes()[:10]
    }

def print_terminal_context():
    """Print the current terminal context."""
    context = get_terminal_context()
    
    print("Terminal Context:")
    print("-" * 50)
    print(f"Current Directory: {context['current_directory']}")
    print(f"Shell: {context['shell_info']['shell']}")
    print(f"User: {context['shell_info']['user']}")
    
    if context['recent_commands']:
        print("\nRecent Commands:")
        for cmd in context['recent_commands'][-5:]:
            print(f"  {cmd}")
    
    if context['environment_vars']:
        print("\nRelevant Environment Variables:")
        for key, value in context['environment_vars'].items():
            print(f"  {key}: {value}")
    
    print("-" * 50)

if __name__ == "__main__":
    print_terminal_context()