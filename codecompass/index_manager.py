import os
import logging
import hashlib
import threading
import time
from typing import Optional, Dict, Any, Callable, List
from pathlib import Path
import pickle

try:
    from .config import AppConfig
    from .model_factory import get_model_factory
except ImportError:
    # 兼容直接运行时导入
    from config import AppConfig
    from model_factory import get_model_factory


logger = logging.getLogger(__name__)


class IndexManager:
    """索引管理器，负责构建、加载和刷新索引"""
    
    def __init__(self, config: AppConfig):
        self.config = config
        self.repo_crc32 = None
        self.repo_crc32_path = os.path.join(self.config.repo_hash_path, "hash.crc32")
        os.makedirs(self.config.repo_hash_path, exist_ok=True)
        if os.path.exists(self.repo_crc32_path):
            with open(self.repo_crc32_path, "r", encoding="utf-8") as f:
                self.repo_crc32 = f.read()
        else:
            self.repo_crc32 = self.calculate_repo_crc32() # 初始化的时候必须先计算一次，不然index过程中的修改会无法触发索引刷新
            with open(self.repo_crc32_path, "w", encoding="utf-8") as f:
                f.write(self.repo_crc32)
        self.refresh_thread: Optional[threading.Thread] = None
        self.stop_refresh = threading.Event()
        
        # 索引实例
        self.bm25_indexer = None
        self.embedding_indexer = None
        self.graph_indexer = None
        
        # 回调函数列表，用于通知索引更新
        self.index_update_callbacks: List[Callable] = []
        
        self._setup_logging()
    
    def add_index_update_callback(self, callback: Callable):
        """添加索引更新回调函数"""
        self.index_update_callbacks.append(callback)
        logger.info("已添加索引更新回调函数")
    
    def remove_index_update_callback(self, callback: Callable):
        """移除索引更新回调函数"""
        if callback in self.index_update_callbacks:
            self.index_update_callbacks.remove(callback)
            logger.info("已移除索引更新回调函数")
    
    def _notify_index_update(self):
        """通知所有回调函数索引已更新"""
        for callback in self.index_update_callbacks:
            try:
                callback()
                logger.info("已通知索引更新回调函数")
            except Exception as e:
                logger.error(f"调用索引更新回调函数时出错: {e}")
    
    def _setup_logging(self):
        """设置日志"""
        log_level = getattr(logging, self.config.log_level.upper())
        logging.getLogger().setLevel(log_level)
    
    def calculate_repo_crc32(self) -> str:
        """计算仓库的CRC32校验和（使用os.walk实现）"""
        hasher = hashlib.md5()
        repo_path = self.config.repo_path
        file_extensions = self.config.file_extensions

        for root, dirs, files in os.walk(repo_path):
            for file in files:
                if not file.endswith(tuple(file_extensions)):
                    continue
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'rb') as f:
                        hasher.update(f.read())
                except Exception as e:
                    logger.warning(f"读取文件 {file_path} 时出错: {e}，跳过该文件")

        return hasher.hexdigest()
    
    def should_rebuild_index(self) -> bool:
        """检查是否需要重建索引"""
        current_crc32 = self.calculate_repo_crc32()
        ret = False
        is_need_save = False
        if self.repo_crc32 is None:
            logger.info("初始化仓库CRC32")
            self.repo_crc32 = current_crc32
            ret = False
            is_need_save = True
        
        if current_crc32 != self.repo_crc32:
            logger.info("检测到代码仓库变更，需要重建索引")
            self.repo_crc32 = current_crc32
            ret = True
            is_need_save = True
        
        if is_need_save:
            with open(self.repo_crc32_path, "w", encoding="utf-8") as f:
                f.write(current_crc32)
        return ret
    
    def build_bm25_index(self, force: bool = False, incremental: bool = False):
        """构建BM25索引"""
        logger.info("开始构建BM25索引...")
        
        try:
            from retriever.index.bm25_index_pro import build_bm25_index
            
            self.bm25_indexer = build_bm25_index(
                repo_path=self.config.repo_path,
                persist_path=self.config.bm25_persist_path,
                file_extensions=self.config.file_extensions,
                force=force,
                incremental=incremental,
                ignore_config=self.config.ignore_config,
                **self.config.index_config.model_dump()
            )
            
            logger.info("BM25索引构建完成")
            
        except Exception as e:
            logger.error(f"构建BM25索引时出错: {e}")
            raise
    
    def build_embedding_index(self, force: bool = False, incremental: bool = False):
        """构建Embedding索引"""
        logger.info("开始构建Embedding索引...")
        
        try:
            from retriever.index.embedding_index_pro import build_embedding_index
            
            self.embedding_indexer = build_embedding_index(
                repo_path=self.config.repo_path,
                persist_path=self.config.embedding_persist_path,
                file_extensions=self.config.file_extensions,
                embedding_config=self.config.embedding_config,
                force=force,
                incremental=incremental,
                ignore_config=self.config.ignore_config,
                **self.config.index_config.model_dump()
            )
            
            logger.info("Embedding索引构建完成")
            
        except Exception as e:
            logger.error(f"构建Embedding索引时出错: {e}")
            raise
    
    def build_graph_index(self):
        """构建Graph索引"""
        logger.info("开始构建Graph索引...")
        
        try:
            from retriever.index.graph_index import build_graph_index
            
            # 构建图索引
            graph = build_graph_index(
                repo_path=self.config.repo_path,
                output_file=self.config.graph_persist_path,
                global_import=True,
                visualize=False,
            )
            
            logger.info("Graph索引构建完成")
            
        except Exception as e:
            logger.error(f"构建Graph索引时出错: {e}")
            raise
    
    def build_all_indexes(self, force: bool = False, incremental: bool = False):
        """构建所有索引"""
        logger.info("开始构建所有索引...")
        
        # 确保在任何线程中都有正确的模块搜索路径，解决codeblocks模块导入问题
        import sys
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 添加项目根目录到sys.path，这样可以找到codecompass包
        project_root = os.path.dirname(current_dir)
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
            logger.debug(f"已在线程中添加项目根目录到模块搜索路径: {project_root}")
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
            logger.debug(f"已在线程中添加当前目录到模块搜索路径: {current_dir}")
        # 关键：添加retriever/index目录，这样可以直接找到codeblocks包
        retriever_index_dir = os.path.join(current_dir, "retriever", "index")
        if retriever_index_dir not in sys.path:
            sys.path.insert(0, retriever_index_dir)
            logger.debug(f"已在线程中添加retriever/index目录到模块搜索路径: {retriever_index_dir}")
        
        # 创建持久化目录
        os.makedirs(self.config.persist_path, exist_ok=True)
        
        # 串行构建索引，避免并发写入问题
        try:
            self.build_bm25_index(force=force, incremental=incremental)
            self.build_embedding_index(force=force, incremental=incremental)
            # self.build_graph_index()
            logger.info("所有索引构建完成")
        except Exception as e:
            logger.error(f"构建索引失败: {e}")
            raise
    
    def _check_bm25_index_exists(self) -> bool:
        """检查BM25索引是否完整存在"""
        if not os.path.exists(self.config.bm25_persist_path):
            return False
        
        # 检查关键文件是否存在
        required_files = ['retriever.json', 'corpus.jsonl', 'params.index.json']
        for file_name in required_files:
            file_path = os.path.join(self.config.bm25_persist_path, file_name)
            if not os.path.exists(file_path):
                logger.warning(f"BM25索引文件缺失: {file_path}")
                return False
        return True
    
    def _check_embedding_index_exists(self) -> bool:
        """检查Embedding索引是否完整存在"""
        if not os.path.exists(self.config.embedding_persist_path):
            return False
        
        # 检查关键文件是否存在
        required_files = ['docstore.json', 'index_store.json', 'default__vector_store.json']
        for file_name in required_files:
            file_path = os.path.join(self.config.embedding_persist_path, file_name)
            if not os.path.exists(file_path):
                logger.warning(f"Embedding索引文件缺失: {file_path}")
                return False
        return True
    
    def _check_graph_index_exists(self) -> bool:
        """检查Graph索引是否存在"""
        return os.path.exists(self.config.graph_persist_path)

    def load_indexes(self):
        """加载索引"""
        logger.info("开始加载索引...")
        
        # 确保在任何线程中都有正确的模块搜索路径，解决codeblocks模块导入问题
        import sys
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 添加项目根目录到sys.path，这样可以找到codecompass包
        project_root = os.path.dirname(current_dir)
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
            logger.debug(f"已在线程中添加项目根目录到模块搜索路径: {project_root}")
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
            logger.debug(f"已在线程中添加当前目录到模块搜索路径: {current_dir}")
        # 关键：添加retriever/index目录，这样可以直接找到codeblocks包
        retriever_index_dir = os.path.join(current_dir, "retriever", "index")
        if retriever_index_dir not in sys.path:
            sys.path.insert(0, retriever_index_dir)
            logger.debug(f"已在线程中添加retriever/index目录到模块搜索路径: {retriever_index_dir}")
        
        try:
            # 检查索引文件是否完整存在
            bm25_exists = self._check_bm25_index_exists()
            embedding_exists = self._check_embedding_index_exists()
            graph_exists = self._check_graph_index_exists()
            
            if not (bm25_exists and embedding_exists and graph_exists):
                logger.info("索引文件不完整，开始构建索引...")
                self.build_all_indexes()
            else:
                logger.info("索引文件存在，加载现有索引...")
                
                # 加载BM25索引
                from retriever.index.bm25_index_pro import BM25IndexPro
                self.bm25_indexer = BM25IndexPro(
                    persist_path=self.config.bm25_persist_path,
                    ignore_config=self.config.ignore_config
                )
                self.bm25_indexer.load_index()
                
                # 加载Embedding索引
                from retriever.index.embedding_index_pro import EmbeddingIndexPro
                self.embedding_indexer = EmbeddingIndexPro(persist_path=self.config.embedding_persist_path, ignore_config=self.config.ignore_config)
                self.embedding_indexer.embedding_config = self.config.embedding_config
                self.embedding_indexer.load_index()
                
                # 图索引不需要特别的加载过程，pipeline会直接从文件加载
                
                logger.info("索引加载完成")
        
        except Exception as e:
            logger.error(f"加载索引时出错: {e}")
            raise
    
    def refresh_indexes_if_needed(self):
        """如果需要，刷新索引"""
        if self.should_rebuild_index():
            logger.info("开始刷新索引...")
            self.build_all_indexes(incremental=True)
            
            # 重新加载索引到pipeline
            self.load_indexes()
            
            # 通知外部索引已更新
            self._notify_index_update()
            
            logger.info("索引刷新完成")
    
    def start_auto_refresh(self):
        """启动自动刷新"""
        if not self.config.index_config.enable_auto_refresh:
            return

        def refresh_loop():
            while not self.stop_refresh.is_set():
                try:
                    self.refresh_indexes_if_needed()
                except Exception as e:
                    logger.error(f"自动刷新索引时出错: {e}")

                # 等待刷新间隔
                self.stop_refresh.wait(self.config.index_config.refresh_interval)

        self.refresh_thread = threading.Thread(target=refresh_loop, daemon=True)
        self.refresh_thread.start()
        logger.info(f"启动自动刷新，间隔: {self.config.index_config.refresh_interval}秒")
    
    def stop_auto_refresh(self):
        """停止自动刷新"""
        if self.refresh_thread:
            self.stop_refresh.set()
            self.refresh_thread.join()
            logger.info("停止自动刷新")
    
    def get_pipeline_paths(self) -> Dict[str, str]:
        """获取pipeline需要的路径"""
        return {
            "bm25_persist_path": self.config.bm25_persist_path,
            "emb_persist_path": self.config.embedding_persist_path,
            "graph_persist_path": self.config.graph_persist_path,
        } 