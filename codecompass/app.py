import os
import json
import sys
import logging
import asyncio
import time

# 添加当前目录到Python模块搜索路径，解决codeblocks模块导入问题
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from litellm import completion
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
from fastapi.logger import logger
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

try:
    from .config import create_config
    from .index_manager import IndexManager
    from .pipeline_pro import Pipeline
    from .model_factory import initialize_model_factory, get_model_factory
except ImportError:
    # 兼容直接运行时导入
    from config import create_config
    from index_manager import IndexManager
    from pipeline_pro import Pipeline
    from model_factory import initialize_model_factory, get_model_factory

from contextlib import asynccontextmanager

# 全局配置和管理器
config = None
index_manager = None
pipeline = None

INTFACE_VERSION = "0.0.1"

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时的初始化已在main函数中完成
    yield
    # 应用关闭时的清理工作
    global index_manager
    if index_manager:
        index_manager.stop_auto_refresh()
        logger.info("应用关闭，已停止索引自动刷新")

app = FastAPI(lifespan=lifespan)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=False,  # 对于origin=null，不能设置为True
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],  # 明确允许的方法
    allow_headers=["*"],  # 允许所有请求头
    expose_headers=["*"],  # 暴露所有响应头
)

def setup_logging(log_level: str):
    """设置日志"""
    level = getattr(logging, log_level.upper())
    logging.basicConfig(
        level=level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )


def initialize_app(app_config):
    """初始化应用"""
    global config, index_manager, pipeline
    
    config = app_config
    setup_logging(config.log_level)
    
    logger.info(f"初始化CodebaseQA服务")
    logger.info(f"仓库路径: {config.repo_path}")
    logger.info(f"持久化路径: {config.persist_path}")
    
    # 初始化模型工厂
    initialize_model_factory(config)
    
    # 初始化索引管理器
    index_manager = IndexManager(config)
    
    # 注册pipeline重新初始化回调函数
    index_manager.add_index_update_callback(reinitialize_pipeline)
    
    # 加载或构建索引
    logger.info("开始加载索引...")
    index_manager.load_indexes()

    # 启动自动刷新
    if config.index_config.enable_auto_refresh:
        index_manager.start_auto_refresh()
        logger.info("已启动索引自动刷新")

    # 初始化pipeline
    initialize_pipeline()
    
    logger.info("应用初始化完成")


def initialize_pipeline():
    """初始化pipeline"""
    global pipeline, index_manager
    
    # 获取pipeline路径
    pipeline_paths = index_manager.get_pipeline_paths()

    # 初始化pipeline
    pipeline = Pipeline(
        bm25_persist_path=pipeline_paths["bm25_persist_path"],
        emb_persist_path=pipeline_paths["emb_persist_path"],
        # graph_persist_path=pipeline_paths["graph_persist_path"],
    )
    logger.info("Pipeline初始化完成")


def reinitialize_pipeline():
    """重新初始化pipeline（当索引更新时调用）"""
    global pipeline
    logger.info("检测到索引更新，重新加载Pipeline索引...")
    try:
        # 如果pipeline已存在，只需重新加载索引
        if pipeline:
            pipeline.reload_indexes()
            logger.info("Pipeline索引重新加载成功")
        else:
            # 如果pipeline不存在，则创建新的pipeline
            initialize_pipeline()
            logger.info("Pipeline重新初始化成功")
    except Exception as e:
        logger.error(f"Pipeline重新初始化失败: {e}")


def generate_context(query: str):
    results = pipeline.run(query)
    logger.debug(results)
    return {
        "query": query,
        "results": results,
    }


def _search_entity(query: str, top_n=None):
    """根据query的实体名搜索相关的实体和其相关的实体"""
    entity_searcher = pipeline.graph_retriever.entity_searcher
    dependency_searcher = pipeline.graph_retriever.dependency_searcher
    
    if not entity_searcher.has_node(query):
        return {"error": "entity not found"}
    
    neighbors = dependency_searcher.get_neighbors(query)[0]
    if isinstance(top_n, int):
        neighbors = neighbors[:top_n]
    neighbors.append(query)
    
    entities = entity_searcher.get_node_data(
        neighbors, return_code_content=True, wrap_with_ln=False
    )
    return {"entities": entities}


def paser_query_for_entity(query: str):
    """从query中解析出实体"""
    
    # 使用模型工厂获取LLM配置
    model_factory = get_model_factory()
    llm_config = model_factory.get_llm_config()
    
    # 设置litellm所需的环境变量
    os.environ["LM_STUDIO_API_BASE"] = llm_config.api_base
    os.environ["LM_STUDIO_API_KEY"] = llm_config.api_key
    
    response = completion(
        model=llm_config.model_name,
        messages=[
            {
                "role": "user",
                "content": query,
            }
        ],
    )
    return response.choices[0].message.content


@app.post("/query")
async def get_query_context(request: Request):
    """
    查询代码库内容
    
    请求参数:
        query (str): 查询字符串
        top_k (int, optional): 返回结果数量，默认10
        include (list, optional): 包含路径模式列表，支持glob模式（如 "*.py", "src/**"）
                                 为空时全量搜索，有值时只搜索匹配的路径
        exclude (list, optional): 排除路径模式列表，支持glob模式
                                 为空时按照默认配置排除，不额外排除，有值时排除匹配的路径
    
    注意: 
        - include/exclude过滤在检索阶段执行，而非结果过滤，提高查询效率
        - 支持标准glob模式: *, **, ?, [abc], {a,b}等
    
    示例:
        {
            "query": "用户认证逻辑",
            "top_k": 15,
            "include": ["src/**/*.py", "auth/**"],
            "exclude": ["**/test/**", "**/*_test.py"]
        }
    """
    request_data = await request.json()
    query = request_data["query"]
    top_k = request_data.get("top_k", 10)
    include = request_data.get("include", [])  # 包含路径模式列表
    exclude = request_data.get("exclude", [])  # 排除路径模式列表
    exclude = exclude + config.ignore_config.all_ignore_patterns

    # 移除@codebase要求，直接处理查询
    if query.startswith("@codebase"):
        query = query.replace("@codebase", "").strip()
    
    try:
        results = pipeline.run(query, top_k=top_k, include=include, exclude=exclude)
        
        # 直接返回原始格式，保持与pipeline_pro.py中ResponseScheme一致
        context = {
            "query": query,
            "results": results
        }
        logger.debug("Generated context for query.")
        return JSONResponse(content=context)
    except Exception as e:
        logger.error(f"查询处理失败: {e}")
        return JSONResponse(
            content={"error": f"查询处理失败: {str(e)}"}, 
            status_code=500
        )

@app.post("/codebase_insight")
async def get_codebase_insight(request: Request):
    """
    获取代码库文件树及每个文件的code insight
    
    请求参数:
        include (list, optional): 包含路径模式列表，支持glob模式（如 "*.py", "src/**"）
                                 为空时全量搜索，有值时只搜索匹配的路径
        exclude (list, optional): 排除路径模式列表，支持glob模式
                                 为空时按照默认配置排除，有值时排除匹配的路径
    
    返回格式:
    {
        "tree": "文件树字符串，每个文件后附加(code insight)",
        "file_count": "包含insight的文件数量",
        "total_nodes": "总节点数量"
    }
    
    示例:
        {
            "include": ["src/**/*.py", "lib/**"],
            "exclude": ["**/test/**", "**/*_test.py"]
        }
    """
    try:
        # 获取请求参数
        request_data = await request.json() if hasattr(request, 'json') else {}
        include = request_data.get("include", [])  # 包含路径模式列表
        exclude = request_data.get("exclude", [])  # 排除路径模式列表
        exclude = exclude + config.ignore_config.all_ignore_patterns
        if not index_manager or not index_manager.embedding_indexer:
            return JSONResponse(
                content={"error": "索引未初始化，请先构建索引"}, 
                status_code=500
            )
        
        # 从embedding索引中获取所有节点（因为只有embedding索引有code insight）
        embedding_indexer = index_manager.embedding_indexer
        
        # 获取所有节点信息
        all_nodes = []
        
        # 方法1：从节点状态文件获取（最可靠的方式）
        try:
            nodes_state = embedding_indexer._load_nodes_state()
            if nodes_state:
                all_nodes = list(nodes_state.values())
                logger.info(f"从节点状态文件获取到 {len(all_nodes)} 个节点")
        except Exception as e:
            logger.warning(f"从节点状态文件获取节点失败: {str(e)}")
        
        # 方法2：如果节点状态文件不存在，尝试从retriever的索引获取
        if not all_nodes:
            try:
                if hasattr(embedding_indexer, '_retriever') and embedding_indexer._retriever:
                    if hasattr(embedding_indexer._retriever, 'index'):
                        index = embedding_indexer._retriever.index
                        if hasattr(index, 'docstore') and hasattr(index.docstore, 'docs'):
                            all_nodes = list(index.docstore.docs.values())
                            logger.info(f"从索引docstore获取到 {len(all_nodes)} 个节点")
            except Exception as e:
                logger.warning(f"从retriever获取节点失败: {str(e)}")
        
        # 方法3：如果都失败，尝试从storage context直接加载
        if not all_nodes and embedding_indexer.persist_path:
            try:
                from llama_index.core import StorageContext, load_index_from_storage
                storage_context = StorageContext.from_defaults(persist_dir=embedding_indexer.persist_path)
                index = load_index_from_storage(storage_context)
                if hasattr(index, 'docstore') and hasattr(index.docstore, 'docs'):
                    all_nodes = list(index.docstore.docs.values())
                    logger.info(f"从storage context获取到 {len(all_nodes)} 个节点")
            except Exception as e:
                logger.warning(f"从storage context获取节点失败: {str(e)}")
        
        if not all_nodes:
            return JSONResponse(
                content={"error": "无法获取索引节点信息，请确保索引已正确构建"}, 
                status_code=500
            )
        
        # 按文件路径分组，收集每个文件的code insights，应用过滤条件
        import fnmatch
        
        def should_include_file(file_path):
            """检查文件是否应该被包含（根据include/exclude模式）"""
            # 处理include逻辑
            if include:
                # 如果有include模式，只保留匹配的文件
                include_match = False
                for pattern in include:
                    if fnmatch.fnmatch(file_path, pattern):
                        include_match = True
                        break
                if not include_match:
                    return False  # 不匹配include模式，排除
            
            # 处理exclude逻辑
            if exclude:
                # 如果有exclude模式，排除匹配的文件
                for pattern in exclude:
                    if fnmatch.fnmatch(file_path, pattern):
                        return False  # 匹配exclude模式，排除
            
            return True  # 通过所有过滤条件
        
        file_insights = {}
        filtered_nodes = []
        
        for node in all_nodes:
            if hasattr(node, 'metadata') and 'file_path' in node.metadata:
                file_path = node.metadata['file_path']
                
                # 应用过滤条件
                if not should_include_file(file_path):
                    continue
                
                filtered_nodes.append(node)
                code_insight = node.metadata.get('code_insight', '')
                
                if code_insight:
                    # 清理code insight，移除注释符号
                    clean_insight = code_insight.strip()
                    if clean_insight.startswith('//'):
                        clean_insight = clean_insight[2:].strip()
                    elif clean_insight.startswith('#'):
                        clean_insight = clean_insight[1:].strip()
                    
                    if clean_insight:
                        # 只为有实际code insight的文件创建entries
                        if file_path not in file_insights:
                            file_insights[file_path] = []
                        
                        if clean_insight not in file_insights[file_path]:
                            file_insights[file_path].append(clean_insight)
        
        # 构建文件树结构，直接传入file_insights
        from workspace.repo_view import get_project_structure
        
        # 直接生成带有code insight的文件树
        enhanced_tree = get_project_structure(config.repo_path, include, exclude, file_insights)
        
        logger.info(f"成功生成带code insight的文件树，包含 {len(file_insights)} 个文件的insights（已过滤）")
        if include or exclude:
            logger.info(f"应用过滤条件：include={include}, exclude={exclude}")
        
        return JSONResponse(content={
            "tree": enhanced_tree,
            "file_count": len(file_insights),
            "total_nodes": len(filtered_nodes),
            "original_nodes": len(all_nodes),
            "filters_applied": {
                "include": include,
                "exclude": exclude
            }
        })
        
    except Exception as e:
        logger.error(f"获取codebase insight失败: {str(e)}")
        return JSONResponse(
            content={"error": f"获取codebase insight失败: {str(e)}"}, 
            status_code=500
        )

# @app.post("/search_entity") # TODO 暂时不使用 实体搜索
async def search_entity(request: Request):
    request_data = await request.json()
    query = request_data.get("query")

    if not query:
        return JSONResponse(
            content={"error": "Query parameter is required"}, status_code=400
        )

    top_n = request_data.get("top_n", None)
    entities = _search_entity(query, top_n)
    if "error" in entities:
        logger.debug(entities)
        entities = []

    data = {
        "interfaceVersion": INTFACE_VERSION,
        "query": {
            "text": query,
            "entity": "",
            "description": "",
            "requirement": "",
        },
        "entities": entities,
    }
    return JSONResponse(content=data)


@app.get("/runtime/v1")
def get_runtime_info():
    """获取运行时信息"""
    return {
        "repo_path": config.repo_path,
        "repo_name": config.repo_name,
        "persist_path": config.persist_path,
        "version": INTFACE_VERSION,
        "auto_refresh": config.index_config.enable_auto_refresh,
        "refresh_interval": config.index_config.refresh_interval,
    }


@app.get("/health")
def health_check():
    """健康检查"""
    try:
        # 检查pipeline是否正常
        pipeline_status = "healthy" if pipeline else "unhealthy"
        
        # 检查索引管理器状态
        index_status = "healthy" if index_manager else "unhealthy"
        
        return {
            "status": "healthy" if pipeline_status == "healthy" and index_status == "healthy" else "unhealthy",
            "version": INTFACE_VERSION,
            "repo_name": config.repo_name if config else "unknown",
            "repo_path": config.repo_path if config else "unknown",
            "pipeline_status": pipeline_status,
            "index_status": index_status,
            "timestamp": time.time()
        }
    except Exception as e:
        return JSONResponse(
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            },
            status_code=500
        )


@app.get("/metrics")
def get_metrics():
    """获取性能指标"""
    try:
        metrics = {
            "system": {
                "repo_path": config.repo_path,
                "repo_name": config.repo_name,
                "persist_path": config.persist_path,
                "auto_refresh_enabled": config.index_config.enable_auto_refresh,
                "refresh_interval": config.index_config.refresh_interval,
            },
            "indexes": {
                "bm25_path": config.bm25_persist_path,
                "embedding_path": config.embedding_persist_path,
                # "graph_path": config.graph_persist_path,
            },
            "runtime": {
                "version": INTFACE_VERSION,
                "uptime": time.time() - start_time if 'start_time' in globals() else 0,
                "log_level": config.log_level,
            }
        }
        return JSONResponse(content=metrics)
    except Exception as e:
        return JSONResponse(
            content={"error": f"获取指标失败: {str(e)}"}, 
            status_code=500
        )


@app.get("/config")
def get_config():
    """获取系统配置"""
    try:
        config_data = {
            "repo_path": config.repo_path,
            "repo_name": config.repo_name,
            "persist_path": config.persist_path,
            "host": config.host,
            "port": config.port,
            "log_level": config.log_level,
            "file_extensions": config.file_extensions,
            "auto_refresh": {
                "enabled": config.index_config.enable_auto_refresh,
                "interval": config.index_config.refresh_interval
            },
            "index_config": {
                "chunk_lines": config.index_config.chunk_lines,
                "chunk_lines_overlap": config.index_config.chunk_lines_overlap,
                "max_chars": config.index_config.max_chars
            },
            "embedding_config": {
                "api_base": config.embedding_config.api_base,
                "model_name": config.embedding_config.model_name
            },
            "llm_config": {
                "api_base": config.llm_config.api_base,
                "model_name": config.llm_config.model_name
            }
        }
        return JSONResponse(content=config_data)
    except Exception as e:
        return JSONResponse(
            content={"error": f"获取配置失败: {str(e)}"}, 
            status_code=500
        )


@app.get("/indexes/status")
def get_indexes_status():
    """获取索引状态"""
    try:
        status = {
            "overall_status": "healthy",
            "indexes": {
                "bm25": {
                    "status": "healthy" if index_manager._check_bm25_index_exists() else "missing",
                    "path": config.bm25_persist_path
                },
                "embedding": {
                    "status": "healthy" if index_manager._check_embedding_index_exists() else "missing", 
                    "path": config.embedding_persist_path
                },
                # "graph": {
                #     "status": "healthy" if index_manager._check_graph_index_exists() else "missing",
                #     "path": config.graph_persist_path
                # }
            },
            "last_update": time.time(),
            "auto_refresh": config.index_config.enable_auto_refresh
        }
        
        # 检查整体状态
        if any(idx["status"] == "missing" for idx in status["indexes"].values()):
            status["overall_status"] = "degraded"
            
        return JSONResponse(content=status)
    except Exception as e:
        return JSONResponse(
            content={"error": f"获取索引状态失败: {str(e)}"}, 
            status_code=500
        )


@app.post("/indexes/update")
def update_indexes():
    """更新索引"""
    try:
        if index_manager.should_rebuild_index():
            logger.info("开始重建索引...")
            index_manager.build_all_indexes(incremental=True)
            
            # 重新加载索引
            index_manager.load_indexes()
            
            # 通知pipeline重新初始化
            index_manager._notify_index_update()
        else:
            logger.info("仓库CRC32未发生变化，不需要重建索引")
        return {
            "status": "success",
            "message": "索引更新完成",
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"重建索引失败: {e}")
        return JSONResponse(
            content={
                "status": "error",
                "error": f"重建索引失败: {str(e)}",
                "timestamp": time.time()
            },
            status_code=500
        )


@app.get("/model/status")
def get_model_status():
    """获取模型状态"""
    try:
        model_factory = get_model_factory()
        
        status = {
            "embedding_model": {
                "api_base": config.embedding_config.api_base,
                "model_name": config.embedding_config.model_name,
                "status": "healthy"  # 简单状态检查
            },
            "llm_model": {
                "api_base": config.llm_config.api_base,
                "model_name": config.llm_config.model_name,
                "status": "healthy"  # 简单状态检查
            },
            "factory_status": "initialized" if model_factory else "error"
        }
        
        return JSONResponse(content=status)
    except Exception as e:
        return JSONResponse(
            content={"error": f"获取模型状态失败: {str(e)}"}, 
            status_code=500
        )


@app.get("/stats")
def get_stats():
    """获取统计信息"""
    try:
        stats = {
            "system_info": {
                "version": INTFACE_VERSION,
                "repo_name": config.repo_name,
                "uptime": time.time() - start_time if 'start_time' in globals() else 0
            },
            "index_stats": {
                "total_indexes": 3,
                "healthy_indexes": sum(1 for check in [
                    index_manager._check_bm25_index_exists(),
                    index_manager._check_embedding_index_exists(), 
                    # index_manager._check_graph_index_exists()
                ] if check)
            },
            "performance": {
                "average_query_time": 0.5,  # TODO: 实际统计
                "total_queries": 0,  # TODO: 实际统计
                "error_rate": 0.0  # TODO: 实际统计
            }
        }
        
        return JSONResponse(content=stats)
    except Exception as e:
        return JSONResponse(
            content={"error": f"获取统计信息失败: {str(e)}"}, 
            status_code=500
        )


def main():
    """主函数"""
    global start_time
    start_time = time.time()
    
    try:
        # 创建配置（支持命令行参数和环境变量）
        app_config = create_config()
    except SystemExit as e:
        # --help 被调用
        if e.code == 0:
            sys.exit(0)
        raise
    except Exception as e:
        print(f"配置错误: {e}")
        sys.exit(1)
    
    # 初始化应用
    initialize_app(app_config)
    
    # 启动服务器
    logger.info(f"启动服务器: http://{config.host}:{config.port}")
    uvicorn.run(app, host=config.host, port=config.port)


if __name__ == "__main__":
    main()
