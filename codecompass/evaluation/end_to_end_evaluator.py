#!/usr/bin/env python3
"""
端到端评测器
通过启动实际的检索服务来进行完整的评测流程
"""

import json
import time
import logging
import subprocess
import requests
import signal
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict
import threading

logger = logging.getLogger(__name__)


@dataclass
class TestCase:
    """测试用例"""
    query: str  # comment_zh
    ground_truth_file: str  # filepath (相对路径)
    repo: str
    original_filepath: str  # 原始完整路径


@dataclass
class QueryResult:
    """查询结果"""
    query: str
    ground_truth_file: str
    predicted_files: List[str]
    scores: List[float]
    execution_time_ms: float
    is_match: bool
    match_rank: Optional[int]


class ServiceManager:
    """服务管理器"""

    def __init__(self, venv_path: str = ".venv"):
        self.venv_path = venv_path
        self.current_process = None
        self.current_port = None

    def start_service(self, repo_path: str, repo_name: str, port: int = 5001) -> bool:
        """启动检索服务"""
        try:
            # 停止当前服务
            logger.info(f"准备启动新服务，先停止当前服务...")
            self.stop_service()

            # 强制清理端口，确保完全释放
            self._force_clear_port(port)

            # 构建命令 - 直接使用python，不通过shell
            python_path = f"{self.venv_path}/bin/python"
            cmd = [python_path, "codecompass/app.py", "--repo-path", repo_path, "--port", str(port)]

            logger.info(f"启动新服务: {repo_name} -> {repo_path} -> 端口 {port}")
            logger.info(f"执行命令: {' '.join(cmd)}")

            # 直接启动python进程，不使用shell
            self.current_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid  # 创建新的进程组
            )
            self.current_port = port

            logger.info(f"新进程已启动，PID: {self.current_process.pid}")

            # 等待服务启动
            success = self._wait_for_service(port, timeout=180)  # 增加到3分钟

            if not success:
                # 如果启动失败，输出错误日志
                if self.current_process and self.current_process.poll() is not None:
                    stdout, stderr = self.current_process.communicate()
                    if stdout:
                        logger.error(f"服务stdout: {stdout.decode('utf-8', errors='ignore')}")
                    if stderr:
                        logger.error(f"服务stderr: {stderr.decode('utf-8', errors='ignore')}")

            return success

        except Exception as e:
            logger.error(f"启动服务失败: {e}")
            return False
    
    def _wait_for_service(self, port: int, timeout: int = 120) -> bool:
        """等待服务启动并确保索引构建完成"""
        health_url = f"http://127.0.0.1:{port}/health"
        query_url = f"http://127.0.0.1:{port}/query"
        start_time = time.time()

        logger.info(f"等待服务启动和索引构建完成...")

        while time.time() - start_time < timeout:
            try:
                # 先尝试health端点
                response = requests.get(health_url, timeout=5)
                if response.status_code == 200:
                    health_data = response.json()
                    # 检查索引状态
                    if health_data.get('status') == 'healthy':
                        logger.info(f"服务已启动且索引构建完成: 端口 {port}")
                        # 额外等待2秒确保索引完全就绪
                        time.sleep(2)
                        return True
                    else:
                        logger.debug(f"服务启动中，索引状态: {health_data.get('status', 'unknown')}")
            except requests.exceptions.RequestException:
                pass

            try:
                # 如果health端点不存在，尝试query端点
                response = requests.post(query_url, json={"query": "test", "top_k": 1}, timeout=5)
                if response.status_code in [200, 400]:  # 400也表示服务在运行
                    logger.info(f"服务已启动: 端口 {port}")
                    # 额外等待2秒确保索引完全就绪
                    time.sleep(2)
                    return True
            except requests.exceptions.RequestException:
                pass

            time.sleep(3)  # 增加等待间隔

        logger.error(f"服务启动超时: 端口 {port}")
        return False
    
    def stop_service(self):
        """停止当前服务"""
        if self.current_process:
            try:
                logger.info(f"正在停止服务 (PID: {self.current_process.pid})...")
                self.current_process.terminate()
                self.current_process.wait(timeout=10)
                logger.info("服务已停止")
            except Exception as e:
                logger.warning(f"停止服务时出现异常: {e}")
                try:
                    self.current_process.kill()
                    self.current_process.wait(timeout=5)
                    logger.info("服务已强制停止")
                except:
                    pass

            self.current_process = None

        if self.current_port:
            # 简单等待端口释放
            logger.info(f"等待端口 {self.current_port} 释放...")
            time.sleep(3)
            self.current_port = None
            logger.info("端口已释放")

        if not self.current_process and not self.current_port:
            logger.info("没有运行中的服务需要停止")

    def _force_clear_port(self, port: int):
        """强制清理指定端口上的所有进程"""
        try:
            logger.info(f"强制清理端口 {port}...")

            # 多次尝试清理端口
            for attempt in range(3):
                # 查找端口上的进程
                result = subprocess.run(f"lsof -ti:{port}",
                                     shell=True, capture_output=True, timeout=5)
                if result.returncode == 0 and result.stdout.strip():
                    pids = result.stdout.decode().strip().split('\n')
                    logger.info(f"第{attempt+1}次尝试: 发现端口 {port} 上的进程: {pids}")

                    # 强制终止这些进程
                    subprocess.run(f"lsof -ti:{port} | xargs kill -9",
                                 shell=True, capture_output=True, timeout=5)
                    logger.info(f"已强制终止端口 {port} 上的进程")

                    # 等待端口释放
                    time.sleep(2)
                else:
                    logger.info(f"端口 {port} 已清空")
                    break

            # 最终等待
            time.sleep(1)
            logger.info(f"端口 {port} 清理完成")

        except Exception as e:
            logger.warning(f"清理端口 {port} 时出现异常: {e}")


class MetricsCalculator:
    """评估指标计算器"""
    
    def calculate_metrics(self, results: List[QueryResult]) -> Dict:
        """计算评估指标"""
        if not results:
            return self._empty_metrics()
        
        total_queries = len(results)
        exact_matches = sum(1 for r in results if r.is_match)
        
        # 计算各K值的指标
        k_values = [1, 3, 5, 10]
        metrics = {
            'total_queries': total_queries,
            'exact_match_count': exact_matches,
            'exact_match_rate': exact_matches / total_queries,
        }
        
        # 计算P@K, MRR等
        for k in k_values:
            precision_at_k = []
            for result in results:
                if len(result.predicted_files) >= k:
                    relevant_in_top_k = 1 if result.ground_truth_file in result.predicted_files[:k] else 0
                    precision_at_k.append(relevant_in_top_k / k)
                else:
                    precision_at_k.append(0.0)
            
            metrics[f'precision_at_{k}'] = sum(precision_at_k) / len(precision_at_k)
        
        # 计算MRR
        mrr_scores = []
        for result in results:
            if result.is_match and result.match_rank:
                mrr_scores.append(1.0 / result.match_rank)
            else:
                mrr_scores.append(0.0)
        
        metrics['mrr'] = sum(mrr_scores) / len(mrr_scores)
        
        # 计算时延统计
        execution_times = [r.execution_time_ms for r in results]
        metrics['avg_query_time_ms'] = sum(execution_times) / len(execution_times)
        metrics['max_query_time_ms'] = max(execution_times)
        metrics['min_query_time_ms'] = min(execution_times)
        
        return metrics
    
    def _empty_metrics(self) -> Dict:
        """空指标"""
        return {
            'total_queries': 0,
            'exact_match_count': 0,
            'exact_match_rate': 0.0,
            'precision_at_1': 0.0,
            'precision_at_3': 0.0,
            'precision_at_5': 0.0,
            'precision_at_10': 0.0,
            'mrr': 0.0,
            'avg_query_time_ms': 0.0,
            'max_query_time_ms': 0.0,
            'min_query_time_ms': 0.0,
        }


class EndToEndEvaluator:
    """端到端评测器"""
    
    def __init__(self, 
                 base_repo_path: str = "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs",
                 venv_path: str = ".venv"):
        self.base_repo_path = Path(base_repo_path)
        self.service_manager = ServiceManager(venv_path)
        self.metrics_calculator = MetricsCalculator()
        
    def load_test_data(self, jsonl_file: str) -> Dict[str, List[TestCase]]:
        """加载测试数据并按repo分组"""
        logger.info(f"加载测试数据: {jsonl_file}")
        
        repo_test_cases = defaultdict(list)
        
        with open(jsonl_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    
                    # 解析每个函数的测试用例
                    for func in data.get('functions', []):
                        if 'comment_zh' not in func or not func['comment_zh'].strip():
                            continue
                        
                        # 提取相对路径
                        original_filepath = data['filepath']
                        relative_path = self._extract_relative_path(original_filepath, data['repo'])
                        
                        test_case = TestCase(
                            query=func['comment_zh'].strip(),
                            ground_truth_file=relative_path,
                            repo=data['repo'],
                            original_filepath=original_filepath
                        )
                        
                        repo_test_cases[data['repo']].append(test_case)
                        
                except Exception as e:
                    logger.warning(f"解析第{line_num}行失败: {e}")
        
        logger.info(f"加载完成: {len(repo_test_cases)} 个仓库")
        for repo, cases in repo_test_cases.items():
            logger.info(f"  - {repo}: {len(cases)} 个测试用例")
        
        return dict(repo_test_cases)
    
    def _extract_relative_path(self, filepath: str, repo: str) -> str:
        """提取相对于仓库根目录的路径"""
        # 找到repo名称在路径中的位置
        parts = filepath.split('/')
        try:
            repo_index = parts.index(repo)
            # 返回repo之后的路径部分
            relative_parts = parts[repo_index + 1:]
            return '/'.join(relative_parts)
        except ValueError:
            # 如果找不到repo名称，返回文件名
            return Path(filepath).name
    
    def query_service(self, query: str, port: int = 5001, top_k: int = 10) -> Tuple[List[str], List[float], float]:
        """查询检索服务"""
        url = f"http://127.0.0.1:{port}/query"
        
        payload = {
            "query": query,
            "top_k": top_k
        }
        
        start_time = time.time()
        try:
            response = requests.post(url, json=payload, timeout=30)
            execution_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                file_paths = []
                scores = []
                
                for result in results:
                    file_path = result.get('file_path', '')
                    score = result.get('score', 0.0)
                    file_paths.append(file_path)
                    scores.append(score)
                
                return file_paths, scores, execution_time
            else:
                logger.error(f"查询失败: {response.status_code} - {response.text}")
                return [], [], execution_time
                
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"查询异常: {e}")
            return [], [], execution_time
    
    def evaluate_repo(self, repo: str, test_cases: List[TestCase]) -> List[QueryResult]:
        """评测单个仓库"""
        logger.info(f"开始评测仓库: {repo} ({len(test_cases)} 个测试用例)")
        
        # 检查仓库路径
        repo_path = self.base_repo_path / repo
        if not repo_path.exists():
            logger.error(f"仓库路径不存在: {repo_path}")
            return []
        
        # 启动服务 - 为每个仓库使用不同端口
        port = 5001 + hash(repo) % 100  # 基于仓库名生成不同端口
        if not self.service_manager.start_service(str(repo_path), repo, port):
            logger.error(f"无法启动服务: {repo}")
            return []
        
        results = []
        
        try:
            # 执行查询
            for i, test_case in enumerate(test_cases, 1):
                logger.debug(f"查询 {i}/{len(test_cases)}: {test_case.query[:50]}...")

                # 在每次查询前检查服务是否还在运行
                try:
                    health_response = requests.get(f"http://127.0.0.1:{port}/health", timeout=5)
                    if health_response.status_code != 200:
                        logger.error(f"服务健康检查失败: {health_response.status_code}")
                        break
                except requests.exceptions.RequestException as e:
                    logger.error(f"服务健康检查异常: {e}")
                    break

                file_paths, scores, exec_time = self.query_service(test_case.query, port)

                # 如果查询失败，跳过这个测试用例
                if not file_paths and exec_time < 10:  # 查询时间太短可能是连接失败
                    logger.warning(f"查询 {i} 可能失败，跳过")
                    continue

                # 检查是否匹配
                is_match = test_case.ground_truth_file in file_paths
                match_rank = None
                if is_match:
                    match_rank = file_paths.index(test_case.ground_truth_file) + 1

                result = QueryResult(
                    query=test_case.query,
                    ground_truth_file=test_case.ground_truth_file,
                    predicted_files=file_paths,
                    scores=scores,
                    execution_time_ms=exec_time,
                    is_match=is_match,
                    match_rank=match_rank
                )

                results.append(result)

                if i % 5 == 0:
                    matches = sum(1 for r in results if r.is_match)
                    logger.info(f"  进度: {i}/{len(test_cases)}, 匹配率: {matches/i*100:.1f}%")
        
        finally:
            # 停止服务
            self.service_manager.stop_service()
        
        matches = sum(1 for r in results if r.is_match)
        logger.info(f"仓库 {repo} 评测完成: {matches}/{len(results)} 匹配")
        
        return results
    
    def run_evaluation(self, jsonl_file: str) -> Dict:
        """运行完整评测"""
        logger.info("开始端到端评测")
        start_time = time.time()

        # 1. 加载测试数据并汇总
        logger.info("=== 步骤1: 加载和汇总测试数据 ===")
        repo_test_cases = self.load_test_data(jsonl_file)

        # 打印汇总信息
        total_test_cases = sum(len(cases) for cases in repo_test_cases.values())
        logger.info(f"数据汇总完成:")
        logger.info(f"  总仓库数: {len(repo_test_cases)}")
        logger.info(f"  总测试用例数: {total_test_cases}")
        for repo, cases in repo_test_cases.items():
            logger.info(f"  - {repo}: {len(cases)} 个测试用例")

        all_results = []
        repo_metrics = {}

        # 2. 逐个仓库评测
        logger.info(f"\n=== 步骤2: 逐个仓库评测 ===")
        for i, (repo, test_cases) in enumerate(repo_test_cases.items(), 1):
            logger.info(f"\n--- 评测仓库 {i}/{len(repo_test_cases)}: {repo} ---")
            try:
                results = self.evaluate_repo(repo, test_cases)
                all_results.extend(results)

                # 计算单个仓库的指标
                repo_metrics[repo] = self.metrics_calculator.calculate_metrics(results)

                # 输出仓库评测结果
                matches = sum(1 for r in results if r.is_match)
                logger.info(f"仓库 {repo} 评测完成: {matches}/{len(results)} 匹配 ({matches/len(results)*100:.1f}%)")

            except Exception as e:
                logger.error(f"评测仓库 {repo} 失败: {e}")
                import traceback
                logger.debug(traceback.format_exc())
                continue
        
        # 计算总体指标
        overall_metrics = self.metrics_calculator.calculate_metrics(all_results)
        
        total_time = time.time() - start_time
        
        # 汇总结果
        summary = {
            'overall_metrics': overall_metrics,
            'repo_metrics': repo_metrics,
            'total_time_seconds': total_time,
            'total_repos': len(repo_test_cases),
            'successful_repos': len(repo_metrics),
        }
        
        self._print_summary(summary)
        
        return summary
    
    def _print_summary(self, summary: Dict):
        """打印评测摘要"""
        print("\n" + "=" * 60)
        print("端到端评测结果摘要")
        print("=" * 60)
        
        overall = summary['overall_metrics']
        print(f"总查询数: {overall['total_queries']}")
        print(f"精确匹配率: {overall['exact_match_rate']:.3f} ({overall['exact_match_count']}/{overall['total_queries']})")
        print(f"平均MRR: {overall['mrr']:.3f}")
        
        print(f"\nPrecision@K:")
        for k in [1, 3, 5, 10]:
            print(f"  P@{k}: {overall[f'precision_at_{k}']:.3f}")
        
        print(f"\n查询时延:")
        print(f"  平均: {overall['avg_query_time_ms']:.2f}ms")
        print(f"  最大: {overall['max_query_time_ms']:.2f}ms")
        print(f"  最小: {overall['min_query_time_ms']:.2f}ms")
        
        print(f"\n总耗时: {summary['total_time_seconds']:.2f}秒")
        print(f"成功评测仓库: {summary['successful_repos']}/{summary['total_repos']}")
        
        print("\n各仓库详情:")
        for repo, metrics in summary['repo_metrics'].items():
            print(f"  {repo}: {metrics['exact_match_rate']:.3f} ({metrics['exact_match_count']}/{metrics['total_queries']})")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="端到端评测工具")
    parser.add_argument("--test-file", default="dataset/test/testset_lite_3.jsonl", help="测试数据文件")
    parser.add_argument("--base-repo-path", 
                       default="/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs",
                       help="仓库基础路径")
    parser.add_argument("--venv-path", default=".venv", help="虚拟环境路径")
    parser.add_argument("--log-level", default="INFO", help="日志级别")
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(
        level=getattr(logging, args.log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    
    # 运行评测
    evaluator = EndToEndEvaluator(args.base_repo_path, args.venv_path)
    
    try:
        summary = evaluator.run_evaluation(args.test_file)
        
        # 保存结果
        output_file = f"evaluation_summary_{int(time.time())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细结果已保存到: {output_file}")
        
    except KeyboardInterrupt:
        print("\n评测被用户中断")
    except Exception as e:
        logger.error(f"评测失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 确保服务被停止
        evaluator.service_manager.stop_service()


if __name__ == "__main__":
    main()
