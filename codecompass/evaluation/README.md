# CodebaseQA 评估框架

这是一个完整的代码检索评估框架，用于测试和评估CodebaseQA系统在ArkTS代码检索任务上的性能。

## 🚀 快速开始

### 1. 快速测试

运行快速测试来验证框架是否正常工作：

```bash
cd /Users/<USER>/ide_dev/repo/CodebaseQA
python codecompass/evaluation/quick_test.py
```

这将：
- 测试一个仓库（XmlGraphicsBatik）
- 执行3个查询
- 生成评估报告

### 2. 完整评估

运行完整评估：

```bash
python codecompass/evaluation/run_evaluation.py
```

## 📊 评估指标

框架计算以下评估指标：

- **P@K (Precision@K)**: 前K个结果中相关文档的比例
- **MRR@K (Mean Reciprocal Rank)**: 平均倒数排名
- **nDCG@K (Normalized Discounted Cumulative Gain)**: 归一化折损累积增益
- **F1@K**: F1分数
- **查询时延**: 平均、最大、最小查询时间

默认计算K=1,3,5,10的指标。

## 🔧 配置选项

### 命令行参数

```bash
# 基本参数
--test-data-file          # 测试数据文件路径
--base-repo-path          # 仓库基础路径
--persist-path            # 索引持久化路径
--output-dir              # 结果输出目录

# 测试控制
--max-queries-per-repo    # 每个仓库最大查询数量
--force-rebuild-index     # 强制重建索引
--repos-to-test          # 指定要测试的仓库列表
--k-values               # 指定K值列表

# 输出控制
--no-detailed-results    # 不保存详细结果
--no-html-report        # 不生成HTML报告
--log-level             # 日志级别

# 快速测试
--quick-test            # 快速测试模式
--quick-test-repos      # 快速测试仓库列表
--quick-test-queries    # 快速测试查询数量
```

### 使用示例

```bash
# 测试指定仓库
python codecompass/evaluation/run_evaluation.py \
    --repos-to-test XmlGraphicsBatik ohos_subsampling_scale_image_view \
    --max-queries-per-repo 10

# 强制重建索引
python codecompass/evaluation/run_evaluation.py \
    --force-rebuild-index \
    --repos-to-test XmlGraphicsBatik

# 快速测试模式
python codecompass/evaluation/run_evaluation.py \
    --quick-test

# 自定义K值
python codecompass/evaluation/run_evaluation.py \
    --k-values 1 5 10 20 \
    --repos-to-test XmlGraphicsBatik
```

## 📁 数据格式

### 测试数据格式

测试数据文件为JSONL格式，每行包含：

```json
{
  "content": "代码内容...",
  "filepath": "/path/to/file.ets",
  "repo": "仓库名称",
  "functions": [
    {
      "name": "函数名",
      "comment_zh": "中文注释（作为查询）",
      "body": "函数体..."
    }
  ],
  "token_count": 726,
  "isArkUI": false
}
```

### 输出结果格式

评估完成后会生成以下文件：

- `aggregated_metrics_*.json`: 聚合评估指标
- `evaluation_results_*.json`: 详细评估结果
- `query_log_*.json`: 查询日志
- `evaluation_report_*.html`: HTML格式报告

## 🏗️ 架构设计

### 核心组件

1. **TestDataLoader**: 加载和解析测试数据
2. **RepoIndexManager**: 管理多个仓库的索引
3. **QueryExecutor**: 执行查询并记录时延
4. **MetricsCalculator**: 计算评估指标
5. **TestRunner**: 协调整个测试流程

### 工作流程

```
测试数据 → 数据加载器 → 仓库索引管理器 → 查询执行器 → 指标计算器 → 结果报告
```

## 📈 性能优化

### 索引缓存

- 索引会被缓存到`persist_path`目录
- 相同仓库的后续测试会复用已构建的索引
- 使用`--force-rebuild-index`强制重建

### 批量处理

- 支持批量查询执行
- 自动记录查询时延统计
- 支持查询进度显示

## 🔍 故障排除

### 常见问题

1. **测试数据文件不存在**
   - 检查文件路径是否正确
   - 确保文件格式为JSONL

2. **仓库路径无效**
   - 检查`base_repo_path`是否正确
   - 确保仓库目录存在

3. **索引构建失败**
   - 检查磁盘空间是否充足
   - 确保网络连接正常（用于模型API调用）
   - 查看日志获取详细错误信息

4. **查询执行慢**
   - 使用`--max-queries-per-repo`限制查询数量
   - 检查模型API响应时间
   - 考虑使用本地模型

### 调试模式

```bash
python codecompass/evaluation/run_evaluation.py \
    --log-level DEBUG \
    --max-queries-per-repo 1 \
    --repos-to-test XmlGraphicsBatik
```

## 📝 开发指南

### 添加新的评估指标

1. 在`MetricsCalculator`类中添加计算方法
2. 更新`EvaluationResult`和`AggregatedMetrics`数据类
3. 修改报告生成逻辑

### 支持新的数据格式

1. 修改`TestDataLoader`的解析逻辑
2. 更新`TestCase`数据类
3. 确保向后兼容性

### 添加并发支持

当前框架为串行处理，可以通过以下方式添加并发：

1. 在`TestRunner`中添加多进程/多线程支持
2. 注意索引文件的并发访问问题
3. 合理控制并发数量避免资源竞争

## 📊 示例结果

```
=== 评估指标摘要 ===
总查询数: 150
精确匹配率: 0.680 (102/150)
有结果查询率: 0.953
平均MRR: 0.723

=== Precision@K ===
P@1: 0.680
P@3: 0.793
P@5: 0.827
P@10: 0.853

=== nDCG@K ===
nDCG@1: 0.680
nDCG@3: 0.756
nDCG@5: 0.789
nDCG@10: 0.812

=== 查询时延 ===
平均时延: 245.67ms
最大时延: 1203.45ms
最小时延: 89.23ms
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进评估框架！
