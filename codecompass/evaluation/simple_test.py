#!/usr/bin/env python3
"""
简化版测试脚本
避免复杂的导入问题，直接测试核心功能
"""

import sys
import os
import json
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger(__name__)


def test_data_loading():
    """测试数据加载"""
    print("=== 测试数据加载 ===")
    
    from codecompass.evaluation.test_data_loader import TestDataLoader
    
    loader = TestDataLoader()
    test_data_file = "dataset/test/arkts_gitee_funCode_summary_highScores.jsonl"
    
    if not Path(test_data_file).exists():
        print(f"❌ 测试数据文件不存在: {test_data_file}")
        return False
    
    try:
        test_data = loader.load_test_data(test_data_file)
        stats = loader.get_statistics()
        
        print(f"✅ 成功加载 {stats['total_repos']} 个仓库, {stats['total_test_cases']} 个测试用例")
        
        # 显示前几个仓库
        print("前5个仓库:")
        for i, (repo_name, repo_stats) in enumerate(list(stats['repos'].items())[:5]):
            print(f"  {i+1}. {repo_name}: {repo_stats['test_cases']} 个测试用例")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return False


def test_metrics_calculation():
    """测试指标计算"""
    print("\n=== 测试指标计算 ===")
    
    from codecompass.evaluation.metrics_calculator import MetricsCalculator
    
    try:
        calculator = MetricsCalculator()
        
        # 测试完美匹配的情况
        result1 = calculator.calculate_single_query_metrics(
            query="获取路径长度",
            ground_truth_file="library/src/main/ets/batik/svggen/SVGPath.ets",
            predicted_files=[
                "library/src/main/ets/batik/svggen/SVGPath.ets",
                "library/src/main/ets/batik/svggen/SVGCircle.ets",
                "other/file.ets"
            ],
            scores=[0.95, 0.8, 0.6]
        )
        
        # 测试无匹配的情况
        result2 = calculator.calculate_single_query_metrics(
            query="测试查询",
            ground_truth_file="target/file.ets",
            predicted_files=[
                "other1/file.ets",
                "other2/file.ets",
                "other3/file.ets"
            ],
            scores=[0.9, 0.8, 0.7]
        )
        
        print(f"✅ 完美匹配测试: MRR={result1.mrr:.3f}, P@1={result1.precision_at_k[1]:.3f}")
        print(f"✅ 无匹配测试: MRR={result2.mrr:.3f}, P@1={result2.precision_at_k[1]:.3f}")
        
        # 测试聚合指标
        metrics = calculator.aggregate_metrics([result1, result2], [100.0, 200.0])
        print(f"✅ 聚合指标: 平均MRR={metrics.avg_mrr:.3f}, 平均时延={metrics.avg_query_time_ms:.1f}ms")
        
        return True
        
    except Exception as e:
        print(f"❌ 指标计算失败: {e}")
        return False


def test_single_repo_query():
    """测试单个仓库的查询功能"""
    print("\n=== 测试单个仓库查询 ===")
    
    # 检查仓库路径
    repo_name = "XmlGraphicsBatik"
    repo_path = f"/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs/{repo_name}"
    
    if not Path(repo_path).exists():
        print(f"❌ 仓库路径不存在: {repo_path}")
        return False
    
    try:
        # 直接使用现有的配置和Pipeline
        from codecompass.config import AppConfig, IndexConfig, EmbeddingConfig, LLMConfig, RerankConfig, IgnoreConfig
        from codecompass.index_manager import IndexManager
        from codecompass.pipeline_pro import Pipeline
        from codecompass.model_factory import initialize_model_factory

        # 创建临时配置
        config = AppConfig(
            repo_path=repo_path,
            persist_path="./temp/simple_test",
            embedding_config=EmbeddingConfig(),
            llm_config=LLMConfig(),
            rerank_config=RerankConfig(),
            ignore_config=IgnoreConfig()
        )
        
        # 初始化模型工厂
        initialize_model_factory(config)
        
        # 创建索引管理器
        index_manager = IndexManager(config)
        
        print(f"正在为仓库 {repo_name} 构建/加载索引...")
        index_manager.load_indexes()
        
        # 创建Pipeline
        pipeline_paths = index_manager.get_pipeline_paths()
        pipeline = Pipeline(
            bm25_persist_path=pipeline_paths["bm25_persist_path"],
            emb_persist_path=pipeline_paths["emb_persist_path"],
        )
        
        # 执行测试查询
        test_query = "获取路径长度"
        print(f"执行测试查询: '{test_query}'")
        
        start_time = time.time()
        results = pipeline.run(test_query, top_k=5)
        query_time = (time.time() - start_time) * 1000
        
        print(f"✅ 查询完成，耗时: {query_time:.2f}ms")
        print(f"✅ 返回 {len(results)} 个结果:")
        
        for i, result in enumerate(results[:3], 1):
            file_path = result.get('file_path', 'N/A')
            score = result.get('score', 0)
            print(f"  {i}. {file_path} (score: {score:.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ 仓库查询测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("CodebaseQA 评估框架 - 简化测试")
    print("=" * 60)
    
    all_passed = True
    
    # 测试1: 数据加载
    if not test_data_loading():
        all_passed = False
    
    # 测试2: 指标计算
    if not test_metrics_calculation():
        all_passed = False
    
    # 测试3: 单个仓库查询
    if not test_single_repo_query():
        all_passed = False
    
    # 总结
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！评估框架基本功能正常")
        print("\n下一步可以:")
        print("1. 运行完整评估: python codecompass/evaluation/run_evaluation.py --quick-test")
        print("2. 查看详细文档: codecompass/evaluation/README.md")
    else:
        print("❌ 部分测试失败，请检查配置和环境")
    print("=" * 60)


if __name__ == "__main__":
    main()
