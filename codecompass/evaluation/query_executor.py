"""
查询执行器
负责执行查询，记录时延，返回结果
"""

import time
import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

# 导入父级模块
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from codecompass.pipeline_pro import Pipeline


@dataclass
class QueryResult:
    """查询结果"""
    query: str
    results: List[Dict]
    execution_time_ms: float
    total_results: int
    top_k: int
    
    def get_file_paths(self) -> List[str]:
        """获取结果中的文件路径列表"""
        return [result.get('file_path', '') for result in self.results]
    
    def get_scores(self) -> List[float]:
        """获取结果中的分数列表"""
        return [result.get('score', 0.0) for result in self.results]


class QueryExecutor:
    """查询执行器"""
    
    def __init__(self, pipeline: Pipeline):
        """
        初始化查询执行器
        
        Args:
            pipeline: 检索管道
        """
        self.pipeline = pipeline
        self.query_history: List[QueryResult] = []
        
    def execute_query(self, query: str, top_k: int = 10, 
                     include: Optional[List[str]] = None, 
                     exclude: Optional[List[str]] = None) -> QueryResult:
        """
        执行单个查询
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            include: 包含路径模式
            exclude: 排除路径模式
            
        Returns:
            查询结果
        """
        logger.debug(f"执行查询: '{query}' (top_k={top_k})")
        
        # 记录开始时间
        start_time = time.time()
        
        try:
            # 执行查询
            results = self.pipeline.run(
                query=query, 
                top_k=top_k, 
                include=include, 
                exclude=exclude
            )
            
            # 计算执行时间
            execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            # 创建查询结果
            query_result = QueryResult(
                query=query,
                results=results,
                execution_time_ms=execution_time,
                total_results=len(results),
                top_k=top_k
            )
            
            # 记录到历史
            self.query_history.append(query_result)
            
            logger.debug(f"查询完成: {len(results)} 个结果, 耗时: {execution_time:.2f}ms")
            
            return query_result
            
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            # 返回空结果
            execution_time = (time.time() - start_time) * 1000
            return QueryResult(
                query=query,
                results=[],
                execution_time_ms=execution_time,
                total_results=0,
                top_k=top_k
            )
    
    def execute_batch_queries(self, queries: List[str], top_k: int = 10,
                            include: Optional[List[str]] = None,
                            exclude: Optional[List[str]] = None) -> List[QueryResult]:
        """
        批量执行查询
        
        Args:
            queries: 查询列表
            top_k: 返回结果数量
            include: 包含路径模式
            exclude: 排除路径模式
            
        Returns:
            查询结果列表
        """
        logger.info(f"开始批量执行 {len(queries)} 个查询")
        
        results = []
        total_time = 0
        
        for i, query in enumerate(queries, 1):
            logger.debug(f"执行查询 {i}/{len(queries)}: {query[:50]}...")
            
            result = self.execute_query(query, top_k, include, exclude)
            results.append(result)
            total_time += result.execution_time_ms
            
            if i % 10 == 0:
                avg_time = total_time / i
                logger.info(f"已完成 {i}/{len(queries)} 个查询, 平均耗时: {avg_time:.2f}ms")
        
        logger.info(f"批量查询完成: {len(results)} 个结果, 总耗时: {total_time:.2f}ms")
        
        return results
    
    def get_query_statistics(self) -> Dict:
        """
        获取查询统计信息
        
        Returns:
            统计信息字典
        """
        if not self.query_history:
            return {
                "total_queries": 0,
                "avg_execution_time_ms": 0,
                "min_execution_time_ms": 0,
                "max_execution_time_ms": 0,
                "avg_results_count": 0,
                "total_execution_time_ms": 0
            }
        
        execution_times = [result.execution_time_ms for result in self.query_history]
        result_counts = [result.total_results for result in self.query_history]
        
        stats = {
            "total_queries": len(self.query_history),
            "avg_execution_time_ms": sum(execution_times) / len(execution_times),
            "min_execution_time_ms": min(execution_times),
            "max_execution_time_ms": max(execution_times),
            "avg_results_count": sum(result_counts) / len(result_counts),
            "total_execution_time_ms": sum(execution_times)
        }
        
        return stats
    
    def get_slow_queries(self, threshold_ms: float = 1000) -> List[QueryResult]:
        """
        获取慢查询列表
        
        Args:
            threshold_ms: 慢查询阈值（毫秒）
            
        Returns:
            慢查询列表
        """
        slow_queries = [
            result for result in self.query_history 
            if result.execution_time_ms > threshold_ms
        ]
        
        # 按执行时间降序排序
        slow_queries.sort(key=lambda x: x.execution_time_ms, reverse=True)
        
        return slow_queries
    
    def get_empty_result_queries(self) -> List[QueryResult]:
        """
        获取无结果查询列表
        
        Returns:
            无结果查询列表
        """
        return [
            result for result in self.query_history 
            if result.total_results == 0
        ]
    
    def clear_history(self):
        """清空查询历史"""
        self.query_history.clear()
        logger.info("查询历史已清空")
    
    def export_query_log(self, file_path: str):
        """
        导出查询日志
        
        Args:
            file_path: 导出文件路径
        """
        import json
        
        log_data = []
        for result in self.query_history:
            log_entry = {
                "query": result.query,
                "execution_time_ms": result.execution_time_ms,
                "total_results": result.total_results,
                "top_k": result.top_k,
                "file_paths": result.get_file_paths(),
                "scores": result.get_scores()
            }
            log_data.append(log_entry)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"查询日志已导出到: {file_path}")


class QueryBenchmark:
    """查询基准测试"""
    
    def __init__(self, executor: QueryExecutor):
        """
        初始化基准测试
        
        Args:
            executor: 查询执行器
        """
        self.executor = executor
    
    def run_performance_test(self, test_queries: List[str], 
                           iterations: int = 3) -> Dict:
        """
        运行性能测试
        
        Args:
            test_queries: 测试查询列表
            iterations: 迭代次数
            
        Returns:
            性能测试结果
        """
        logger.info(f"开始性能测试: {len(test_queries)} 个查询, {iterations} 次迭代")
        
        all_results = []
        
        for iteration in range(iterations):
            logger.info(f"执行第 {iteration + 1}/{iterations} 次迭代")
            
            # 清空历史以获得干净的统计
            self.executor.clear_history()
            
            # 执行查询
            results = self.executor.execute_batch_queries(test_queries)
            all_results.extend(results)
        
        # 计算统计信息
        execution_times = [result.execution_time_ms for result in all_results]
        
        performance_stats = {
            "test_queries_count": len(test_queries),
            "iterations": iterations,
            "total_executions": len(all_results),
            "avg_execution_time_ms": sum(execution_times) / len(execution_times),
            "min_execution_time_ms": min(execution_times),
            "max_execution_time_ms": max(execution_times),
            "p50_execution_time_ms": sorted(execution_times)[len(execution_times) // 2],
            "p95_execution_time_ms": sorted(execution_times)[int(len(execution_times) * 0.95)],
            "p99_execution_time_ms": sorted(execution_times)[int(len(execution_times) * 0.99)],
        }
        
        logger.info(f"性能测试完成: 平均耗时 {performance_stats['avg_execution_time_ms']:.2f}ms")
        
        return performance_stats


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 这里需要一个有效的Pipeline实例进行测试
    # 实际使用时会从RepoIndexManager获取
    print("QueryExecutor测试需要有效的Pipeline实例")
