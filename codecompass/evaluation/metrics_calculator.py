"""
评估指标计算器
计算P@K, MRR@K, nDCG@K, F1等评估指标
"""

import math
import logging
from typing import List, Dict, Set, Tuple, Optional
from dataclasses import dataclass
import numpy as np

logger = logging.getLogger(__name__)


@dataclass
class EvaluationResult:
    """单个查询的评估结果"""
    query: str
    ground_truth: str
    predicted_files: List[str]
    scores: List[float]
    
    # 指标
    precision_at_k: Dict[int, float]
    recall_at_k: Dict[int, float]
    f1_at_k: Dict[int, float]
    mrr: float
    ndcg_at_k: Dict[int, float]
    
    # 匹配信息
    is_exact_match: bool
    match_rank: Optional[int]  # 第一个匹配的排名（1-based），None表示无匹配


@dataclass
class AggregatedMetrics:
    """聚合评估指标"""
    total_queries: int
    
    # 平均指标
    avg_precision_at_k: Dict[int, float]
    avg_recall_at_k: Dict[int, float]
    avg_f1_at_k: Dict[int, float]
    avg_mrr: float
    avg_ndcg_at_k: Dict[int, float]
    
    # 匹配统计
    exact_match_count: int
    exact_match_rate: float
    queries_with_results: int
    queries_with_results_rate: float
    
    # 时延统计
    avg_query_time_ms: float
    max_query_time_ms: float
    min_query_time_ms: float


class MetricsCalculator:
    """评估指标计算器"""
    
    def __init__(self, k_values: List[int] = None):
        """
        初始化评估指标计算器
        
        Args:
            k_values: 要计算的K值列表，默认为[1, 3, 5, 10]
        """
        self.k_values = k_values or [1, 3, 5, 10]
        
    def calculate_single_query_metrics(self, 
                                     query: str,
                                     ground_truth_file: str,
                                     predicted_files: List[str],
                                     scores: List[float] = None) -> EvaluationResult:
        """
        计算单个查询的评估指标
        
        Args:
            query: 查询文本
            ground_truth_file: 正确答案文件路径
            predicted_files: 预测的文件路径列表
            scores: 对应的分数列表
            
        Returns:
            评估结果
        """
        if scores is None:
            scores = [1.0] * len(predicted_files)
        
        # 标准化文件路径（移除开头的斜杠，统一路径分隔符）
        ground_truth_normalized = self._normalize_path(ground_truth_file)
        predicted_normalized = [self._normalize_path(path) for path in predicted_files]
        
        # 计算匹配信息
        is_exact_match = ground_truth_normalized in predicted_normalized
        match_rank = None
        if is_exact_match:
            match_rank = predicted_normalized.index(ground_truth_normalized) + 1
        
        # 计算各K值的指标
        precision_at_k = {}
        recall_at_k = {}
        f1_at_k = {}
        ndcg_at_k = {}
        
        for k in self.k_values:
            if k <= len(predicted_normalized):
                # 取前K个结果
                top_k_predicted = predicted_normalized[:k]
                top_k_scores = scores[:k]
                
                # 计算Precision@K和Recall@K
                relevant_in_top_k = 1 if ground_truth_normalized in top_k_predicted else 0
                precision_at_k[k] = relevant_in_top_k / k
                recall_at_k[k] = relevant_in_top_k  # 因为只有一个相关文档
                
                # 计算F1@K
                if precision_at_k[k] + recall_at_k[k] > 0:
                    f1_at_k[k] = 2 * precision_at_k[k] * recall_at_k[k] / (precision_at_k[k] + recall_at_k[k])
                else:
                    f1_at_k[k] = 0.0
                
                # 计算nDCG@K
                ndcg_at_k[k] = self._calculate_ndcg_at_k(
                    ground_truth_normalized, top_k_predicted, top_k_scores, k
                )
            else:
                # 如果预测结果少于K个
                precision_at_k[k] = 0.0
                recall_at_k[k] = 0.0
                f1_at_k[k] = 0.0
                ndcg_at_k[k] = 0.0
        
        # 计算MRR
        mrr = 1.0 / match_rank if match_rank else 0.0
        
        return EvaluationResult(
            query=query,
            ground_truth=ground_truth_file,
            predicted_files=predicted_files,
            scores=scores,
            precision_at_k=precision_at_k,
            recall_at_k=recall_at_k,
            f1_at_k=f1_at_k,
            mrr=mrr,
            ndcg_at_k=ndcg_at_k,
            is_exact_match=is_exact_match,
            match_rank=match_rank
        )
    
    def _normalize_path(self, file_path: str) -> str:
        """
        标准化文件路径
        
        Args:
            file_path: 原始文件路径
            
        Returns:
            标准化后的路径
        """
        if not file_path:
            return ""
        
        # 移除开头的斜杠
        path = file_path.lstrip('/')
        
        # 统一使用正斜杠
        path = path.replace('\\', '/')
        
        return path
    
    def _calculate_ndcg_at_k(self, ground_truth: str, predicted: List[str], 
                           scores: List[float], k: int) -> float:
        """
        计算nDCG@K
        
        Args:
            ground_truth: 正确答案
            predicted: 预测结果列表
            scores: 分数列表
            k: K值
            
        Returns:
            nDCG@K值
        """
        # 计算DCG@K
        dcg = 0.0
        for i, pred_file in enumerate(predicted[:k]):
            if pred_file == ground_truth:
                # 相关性为1，不相关为0
                relevance = 1.0
                dcg += relevance / math.log2(i + 2)  # i+2 因为log2(1)=0
        
        # 计算IDCG@K（理想情况下的DCG）
        # 对于单个相关文档，IDCG就是1/log2(2) = 1
        idcg = 1.0
        
        # 计算nDCG@K
        if idcg > 0:
            return dcg / idcg
        else:
            return 0.0
    
    def aggregate_metrics(self, evaluation_results: List[EvaluationResult],
                         query_times_ms: List[float] = None) -> AggregatedMetrics:
        """
        聚合多个查询的评估指标
        
        Args:
            evaluation_results: 评估结果列表
            query_times_ms: 查询时间列表（毫秒）
            
        Returns:
            聚合指标
        """
        if not evaluation_results:
            return self._create_empty_aggregated_metrics()
        
        total_queries = len(evaluation_results)
        
        # 聚合各K值的指标
        avg_precision_at_k = {}
        avg_recall_at_k = {}
        avg_f1_at_k = {}
        avg_ndcg_at_k = {}
        
        for k in self.k_values:
            precision_values = [result.precision_at_k.get(k, 0.0) for result in evaluation_results]
            recall_values = [result.recall_at_k.get(k, 0.0) for result in evaluation_results]
            f1_values = [result.f1_at_k.get(k, 0.0) for result in evaluation_results]
            ndcg_values = [result.ndcg_at_k.get(k, 0.0) for result in evaluation_results]
            
            avg_precision_at_k[k] = sum(precision_values) / total_queries
            avg_recall_at_k[k] = sum(recall_values) / total_queries
            avg_f1_at_k[k] = sum(f1_values) / total_queries
            avg_ndcg_at_k[k] = sum(ndcg_values) / total_queries
        
        # 计算平均MRR
        mrr_values = [result.mrr for result in evaluation_results]
        avg_mrr = sum(mrr_values) / total_queries
        
        # 计算匹配统计
        exact_match_count = sum(1 for result in evaluation_results if result.is_exact_match)
        exact_match_rate = exact_match_count / total_queries
        
        queries_with_results = sum(1 for result in evaluation_results if result.predicted_files)
        queries_with_results_rate = queries_with_results / total_queries
        
        # 计算时延统计
        if query_times_ms:
            avg_query_time_ms = sum(query_times_ms) / len(query_times_ms)
            max_query_time_ms = max(query_times_ms)
            min_query_time_ms = min(query_times_ms)
        else:
            avg_query_time_ms = 0.0
            max_query_time_ms = 0.0
            min_query_time_ms = 0.0
        
        return AggregatedMetrics(
            total_queries=total_queries,
            avg_precision_at_k=avg_precision_at_k,
            avg_recall_at_k=avg_recall_at_k,
            avg_f1_at_k=avg_f1_at_k,
            avg_mrr=avg_mrr,
            avg_ndcg_at_k=avg_ndcg_at_k,
            exact_match_count=exact_match_count,
            exact_match_rate=exact_match_rate,
            queries_with_results=queries_with_results,
            queries_with_results_rate=queries_with_results_rate,
            avg_query_time_ms=avg_query_time_ms,
            max_query_time_ms=max_query_time_ms,
            min_query_time_ms=min_query_time_ms
        )
    
    def _create_empty_aggregated_metrics(self) -> AggregatedMetrics:
        """创建空的聚合指标"""
        return AggregatedMetrics(
            total_queries=0,
            avg_precision_at_k={k: 0.0 for k in self.k_values},
            avg_recall_at_k={k: 0.0 for k in self.k_values},
            avg_f1_at_k={k: 0.0 for k in self.k_values},
            avg_mrr=0.0,
            avg_ndcg_at_k={k: 0.0 for k in self.k_values},
            exact_match_count=0,
            exact_match_rate=0.0,
            queries_with_results=0,
            queries_with_results_rate=0.0,
            avg_query_time_ms=0.0,
            max_query_time_ms=0.0,
            min_query_time_ms=0.0
        )
    
    def print_metrics_summary(self, metrics: AggregatedMetrics):
        """打印指标摘要"""
        print("=== 评估指标摘要 ===")
        print(f"总查询数: {metrics.total_queries}")
        print(f"精确匹配率: {metrics.exact_match_rate:.3f} ({metrics.exact_match_count}/{metrics.total_queries})")
        print(f"有结果查询率: {metrics.queries_with_results_rate:.3f}")
        print(f"平均MRR: {metrics.avg_mrr:.3f}")
        
        print("\n=== Precision@K ===")
        for k in sorted(metrics.avg_precision_at_k.keys()):
            print(f"P@{k}: {metrics.avg_precision_at_k[k]:.3f}")
        
        print("\n=== Recall@K ===")
        for k in sorted(metrics.avg_recall_at_k.keys()):
            print(f"R@{k}: {metrics.avg_recall_at_k[k]:.3f}")
        
        print("\n=== F1@K ===")
        for k in sorted(metrics.avg_f1_at_k.keys()):
            print(f"F1@{k}: {metrics.avg_f1_at_k[k]:.3f}")
        
        print("\n=== nDCG@K ===")
        for k in sorted(metrics.avg_ndcg_at_k.keys()):
            print(f"nDCG@{k}: {metrics.avg_ndcg_at_k[k]:.3f}")
        
        print("\n=== 查询时延 ===")
        print(f"平均时延: {metrics.avg_query_time_ms:.2f}ms")
        print(f"最大时延: {metrics.max_query_time_ms:.2f}ms")
        print(f"最小时延: {metrics.min_query_time_ms:.2f}ms")


if __name__ == "__main__":
    # 测试代码
    calculator = MetricsCalculator()
    
    # 测试单个查询指标计算
    result = calculator.calculate_single_query_metrics(
        query="获取路径长度",
        ground_truth_file="library/src/main/ets/batik/svggen/SVGPath.ets",
        predicted_files=[
            "library/src/main/ets/batik/svggen/SVGPath.ets",
            "library/src/main/ets/batik/svggen/SVGCircle.ets",
            "other/file.ets"
        ],
        scores=[0.95, 0.8, 0.6]
    )
    
    print("=== 单个查询测试 ===")
    print(f"查询: {result.query}")
    print(f"精确匹配: {result.is_exact_match}")
    print(f"匹配排名: {result.match_rank}")
    print(f"MRR: {result.mrr:.3f}")
    
    for k in [1, 3, 5, 10]:
        print(f"P@{k}: {result.precision_at_k.get(k, 0):.3f}")
        print(f"nDCG@{k}: {result.ndcg_at_k.get(k, 0):.3f}")
