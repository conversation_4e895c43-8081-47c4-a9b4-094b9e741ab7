# CodebaseQA 端到端评测系统

这是一个完整的端到端评测系统，通过启动实际的检索服务来进行真实的评测。

## 🚀 快速开始

### 1. 快速测试（推荐）

使用3条测试数据进行快速验证：

```bash
cd /Users/<USER>/ide_dev/repo/CodebaseQA
python codecompass/evaluation/quick_e2e_test.py
```

### 2. 完整评测

使用完整的测试数据集：

```bash
# 使用默认的3条测试数据
python codecompass/evaluation/end_to_end_evaluator.py

# 使用完整的测试数据集
python codecompass/evaluation/end_to_end_evaluator.py \
    --test-file dataset/test/arkts_gitee_funCode_summary_highScores.jsonl

# 自定义参数
python codecompass/evaluation/end_to_end_evaluator.py \
    --test-file dataset/test/testset_lite_3.jsonl \
    --base-repo-path /path/to/repos \
    --venv-path .venv \
    --log-level DEBUG
```

## 📋 工作流程

端到端评测系统的完整工作流程：

```
1. 解析JSONL测试文件
   ↓
2. 按repo分组测试用例
   ↓
3. 对每个repo：
   a. 启动检索服务 (codecompass/app.py)
   b. 等待服务就绪
   c. 发送查询请求
   d. 收集结果
   e. 停止服务
   ↓
4. 计算评估指标
   ↓
5. 生成汇总报告
```

## 📊 评估指标

系统计算以下评估指标：

- **精确匹配率**: Ground Truth文件在结果中的比例
- **P@K (Precision@K)**: 前K个结果中相关文档的比例 (K=1,3,5,10)
- **MRR (Mean Reciprocal Rank)**: 平均倒数排名
- **查询时延**: 平均、最大、最小查询时间

## 📁 测试数据格式

测试数据为JSONL格式，每行包含：

```json
{
  "content": "代码内容...",
  "filepath": "/完整/文件/路径.ets",
  "repo": "仓库名称",
  "functions": [
    {
      "name": "函数名",
      "comment_zh": "中文注释（作为查询）",
      "body": "函数体..."
    }
  ]
}
```

## 🔧 系统要求

### 环境要求
- Python 3.8+
- 虚拟环境 (.venv)
- 可访问的仓库路径

### 依赖包
- requests
- subprocess (内置)
- json (内置)
- pathlib (内置)

### 服务要求
- codecompass/app.py 能够正常启动
- 支持 `/query` POST 接口
- 返回格式：`{"results": [{"file_path": "...", "score": 0.95}]}`

## 📈 示例结果

```
============================================================
端到端评测结果摘要
============================================================
总查询数: 5
精确匹配率: 0.600 (3/5)
平均MRR: 0.600

Precision@K:
  P@1: 0.600
  P@3: 0.200
  P@5: 0.120
  P@10: 0.000

查询时延:
  平均: 149.19ms
  最大: 481.76ms
  最小: 0.57ms

总耗时: 4.79秒
成功评测仓库: 2/2

各仓库详情:
  XmlGraphicsBatik: 1.000 (3/3)
  ohos_subsampling_scale_image_view: 0.000 (0/2)
```

## 🔍 故障排除

### 常见问题

1. **服务启动失败**
   ```
   错误: 服务启动超时
   解决: 检查虚拟环境路径和仓库路径是否正确
   ```

2. **查询连接失败**
   ```
   错误: Connection refused
   解决: 确保服务完全启动，增加等待时间
   ```

3. **仓库路径不存在**
   ```
   错误: 仓库路径不存在
   解决: 检查base_repo_path配置是否正确
   ```

### 调试模式

```bash
python codecompass/evaluation/end_to_end_evaluator.py \
    --log-level DEBUG \
    --test-file dataset/test/testset_lite_3.jsonl
```

## 📝 输出文件

评测完成后会生成：

- `evaluation_summary_<timestamp>.json`: 详细评测结果
- `quick_e2e_test_result_<timestamp>.json`: 快速测试结果

结果文件包含：
- 总体指标
- 各仓库详细指标
- 性能统计
- 时间戳信息

## 🎯 性能优化建议

1. **并行处理**: 目前是串行处理各仓库，可以考虑并行化
2. **服务复用**: 对于相同仓库的多次查询，可以复用服务实例
3. **缓存机制**: 对于重复查询，可以添加缓存
4. **批量查询**: 支持批量查询接口以减少网络开销

## 🔄 扩展功能

### 添加新的评估指标

1. 在 `MetricsCalculator.calculate_metrics()` 中添加计算逻辑
2. 更新结果输出格式
3. 修改摘要显示

### 支持新的数据格式

1. 修改 `load_test_data()` 方法
2. 更新 `TestCase` 数据类
3. 确保向后兼容性

### 添加并发支持

1. 使用 `concurrent.futures` 或 `asyncio`
2. 注意端口分配和资源管理
3. 处理并发异常情况

## 📞 技术支持

如果遇到问题，请：

1. 检查日志输出获取详细错误信息
2. 确认所有依赖和路径配置正确
3. 尝试使用快速测试验证基本功能
4. 查看生成的结果文件分析问题

## 🎉 成功案例

使用3条测试数据的快速测试：
- ✅ XmlGraphicsBatik: 100% 匹配率 (3/3)
- ⚠️ ohos_subsampling_scale_image_view: 需要进一步调试

总体表现：60% 精确匹配率，平均查询时延 149ms
