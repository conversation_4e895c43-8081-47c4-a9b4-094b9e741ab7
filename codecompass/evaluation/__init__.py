"""
CodebaseQA 评估模块

该模块提供了完整的代码检索评估框架，包括：
- 测试数据加载和解析
- 多仓库索引管理
- 查询执行和时延统计
- 评估指标计算（P@K, MRR@K, nDCG@K, F1等）
- 详细结果报告生成

主要组件：
- TestDataLoader: 测试数据加载器
- RepoIndexManager: 仓库索引管理器
- QueryExecutor: 查询执行器
- MetricsCalculator: 评估指标计算器
- TestRunner: 测试主流程

使用示例：
    from codecompass.evaluation import TestRunner
    
    runner = TestRunner(
        test_data_file="dataset/test/arkts_gitee_funCode_summary_highScores.jsonl",
        base_repo_path="/path/to/repos",
        persist_path="./temp/evaluation"
    )
    
    metrics = runner.run_full_evaluation(
        max_queries_per_repo=10,
        repos_to_test=["repo1", "repo2"]
    )
    
    runner.save_detailed_results("./results")
"""

from .test_data_loader import TestDataLoader, TestCase, RepoTestData
from .repo_index_manager import RepoIndexManager
from .query_executor import QueryExecutor, QueryResult, QueryBenchmark
from .metrics_calculator import MetricsCalculator, EvaluationResult, AggregatedMetrics
from .test_runner import TestRunner
from .config import EvaluationConfig, create_evaluation_config

__all__ = [
    # 数据加载
    "TestDataLoader",
    "TestCase", 
    "RepoTestData",
    
    # 索引管理
    "RepoIndexManager",
    
    # 查询执行
    "QueryExecutor",
    "QueryResult",
    "QueryBenchmark",
    
    # 指标计算
    "MetricsCalculator",
    "EvaluationResult",
    "AggregatedMetrics",
    
    # 主流程
    "TestRunner",
    
    # 配置
    "EvaluationConfig",
    "create_evaluation_config",
]

__version__ = "1.0.0"
__author__ = "CodebaseQA Team"
__description__ = "CodebaseQA 评估框架"
