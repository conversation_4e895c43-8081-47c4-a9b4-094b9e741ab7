"""
测试主流程
协调各个组件，执行完整的测试流程
"""

import json
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import asdict
import traceback

logger = logging.getLogger(__name__)

from .test_data_loader import TestDataLoader, TestCase, RepoTestData
from .repo_index_manager import RepoIndexManager
from .query_executor import QueryExecutor, QueryResult
from .metrics_calculator import MetricsCalculator, EvaluationResult, AggregatedMetrics


class TestRunner:
    """测试主流程"""
    
    def __init__(self, 
                 test_data_file: str,
                 base_repo_path: str = "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs",
                 persist_path: str = "./temp/evaluation",
                 k_values: List[int] = None):
        """
        初始化测试运行器
        
        Args:
            test_data_file: 测试数据文件路径
            base_repo_path: 仓库基础路径
            persist_path: 索引持久化路径
            k_values: 要计算的K值列表
        """
        self.test_data_file = test_data_file
        self.base_repo_path = base_repo_path
        self.persist_path = persist_path
        self.k_values = k_values or [1, 3, 5, 10]
        
        # 初始化组件
        self.data_loader = TestDataLoader(base_repo_path)
        self.repo_manager = RepoIndexManager(persist_path)
        self.metrics_calculator = MetricsCalculator(k_values)
        
        # 测试结果
        self.test_data: Dict[str, RepoTestData] = {}
        self.evaluation_results: List[EvaluationResult] = []
        self.query_results: List[QueryResult] = []
        self.aggregated_metrics: Optional[AggregatedMetrics] = None
        
        logger.info(f"初始化测试运行器")
        logger.info(f"  测试数据文件: {test_data_file}")
        logger.info(f"  仓库基础路径: {base_repo_path}")
        logger.info(f"  持久化路径: {persist_path}")
    
    def run_full_evaluation(self, 
                          max_queries_per_repo: Optional[int] = None,
                          force_rebuild_index: bool = False,
                          repos_to_test: Optional[List[str]] = None) -> AggregatedMetrics:
        """
        运行完整评估
        
        Args:
            max_queries_per_repo: 每个仓库最大查询数量（用于快速测试）
            force_rebuild_index: 是否强制重建索引
            repos_to_test: 要测试的仓库列表，None表示测试所有仓库
            
        Returns:
            聚合评估指标
        """
        logger.info("开始完整评估流程")
        start_time = time.time()
        
        try:
            # 1. 加载测试数据
            logger.info("=== 步骤1: 加载测试数据 ===")
            self.test_data = self.data_loader.load_test_data(self.test_data_file)
            
            # 验证仓库路径
            validation_results = self.data_loader.validate_repo_paths()
            invalid_repos = [repo for repo, valid in validation_results.items() if not valid]
            if invalid_repos:
                logger.warning(f"以下仓库路径无效，将跳过: {invalid_repos}")
            
            # 过滤要测试的仓库
            repos_to_evaluate = []
            for repo_name, repo_data in self.test_data.items():
                if repos_to_test and repo_name not in repos_to_test:
                    continue
                if repo_name in invalid_repos:
                    continue
                repos_to_evaluate.append((repo_name, repo_data))
            
            logger.info(f"将评估 {len(repos_to_evaluate)} 个仓库")
            
            # 2. 逐个仓库进行评估
            total_queries = 0
            successful_queries = 0
            
            for i, (repo_name, repo_data) in enumerate(repos_to_evaluate, 1):
                logger.info(f"=== 步骤2.{i}: 评估仓库 {repo_name} ({i}/{len(repos_to_evaluate)}) ===")
                
                try:
                    repo_results = self._evaluate_single_repo(
                        repo_name, 
                        repo_data, 
                        max_queries_per_repo,
                        force_rebuild_index
                    )
                    
                    self.evaluation_results.extend(repo_results['evaluation_results'])
                    self.query_results.extend(repo_results['query_results'])
                    
                    total_queries += repo_results['total_queries']
                    successful_queries += repo_results['successful_queries']
                    
                    logger.info(f"仓库 {repo_name} 评估完成: {repo_results['successful_queries']}/{repo_results['total_queries']} 查询成功")
                    
                except Exception as e:
                    logger.error(f"仓库 {repo_name} 评估失败: {e}")
                    logger.debug(traceback.format_exc())
                    continue
            
            # 3. 计算聚合指标
            logger.info("=== 步骤3: 计算聚合指标 ===")
            query_times = [result.execution_time_ms for result in self.query_results]
            self.aggregated_metrics = self.metrics_calculator.aggregate_metrics(
                self.evaluation_results, query_times
            )
            
            # 4. 输出结果
            total_time = time.time() - start_time
            logger.info(f"=== 评估完成 ===")
            logger.info(f"总耗时: {total_time:.2f}秒")
            logger.info(f"总查询数: {total_queries}")
            logger.info(f"成功查询数: {successful_queries}")
            logger.info(f"成功率: {successful_queries/total_queries*100:.1f}%" if total_queries > 0 else "N/A")
            
            # 打印指标摘要
            self.metrics_calculator.print_metrics_summary(self.aggregated_metrics)
            
            return self.aggregated_metrics
            
        except Exception as e:
            logger.error(f"评估流程失败: {e}")
            logger.debug(traceback.format_exc())
            raise
        finally:
            # 清理资源
            self.repo_manager.cleanup()
    
    def _evaluate_single_repo(self, 
                            repo_name: str, 
                            repo_data: RepoTestData,
                            max_queries: Optional[int] = None,
                            force_rebuild_index: bool = False) -> Dict:
        """
        评估单个仓库
        
        Args:
            repo_name: 仓库名称
            repo_data: 仓库测试数据
            max_queries: 最大查询数量
            force_rebuild_index: 是否强制重建索引
            
        Returns:
            评估结果字典
        """
        logger.info(f"开始评估仓库: {repo_name}")
        logger.info(f"  仓库路径: {repo_data.repo_path}")
        logger.info(f"  测试用例数: {len(repo_data.test_cases)}")
        
        # 1. 切换到目标仓库
        if not self.repo_manager.switch_to_repo(repo_name, repo_data.repo_path, force_rebuild_index):
            raise Exception(f"无法切换到仓库: {repo_name}")
        
        # 2. 获取Pipeline
        pipeline = self.repo_manager.get_current_pipeline()
        if not pipeline:
            raise Exception(f"无法获取仓库 {repo_name} 的Pipeline")
        
        # 3. 创建查询执行器
        executor = QueryExecutor(pipeline)
        
        # 4. 准备测试用例
        test_cases = repo_data.test_cases
        if max_queries and len(test_cases) > max_queries:
            test_cases = test_cases[:max_queries]
            logger.info(f"限制查询数量为: {max_queries}")
        
        # 5. 执行查询
        evaluation_results = []
        query_results = []
        successful_queries = 0
        
        for i, test_case in enumerate(test_cases, 1):
            try:
                logger.debug(f"执行查询 {i}/{len(test_cases)}: {test_case.query[:50]}...")
                
                # 执行查询
                query_result = executor.execute_query(test_case.query, top_k=10)
                query_results.append(query_result)
                
                # 计算评估指标
                predicted_files = query_result.get_file_paths()
                scores = query_result.get_scores()
                
                eval_result = self.metrics_calculator.calculate_single_query_metrics(
                    query=test_case.query,
                    ground_truth_file=test_case.ground_truth_file,
                    predicted_files=predicted_files,
                    scores=scores
                )
                evaluation_results.append(eval_result)
                
                if eval_result.is_exact_match:
                    successful_queries += 1
                
                # 定期输出进度
                if i % 10 == 0:
                    success_rate = successful_queries / i * 100
                    logger.info(f"  进度: {i}/{len(test_cases)}, 成功率: {success_rate:.1f}%")
                
            except Exception as e:
                logger.warning(f"查询执行失败 ({i}/{len(test_cases)}): {e}")
                continue
        
        return {
            'evaluation_results': evaluation_results,
            'query_results': query_results,
            'total_queries': len(test_cases),
            'successful_queries': successful_queries
        }
    
    def save_detailed_results(self, output_dir: str):
        """
        保存详细结果
        
        Args:
            output_dir: 输出目录
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        # 1. 保存聚合指标
        if self.aggregated_metrics:
            metrics_file = output_path / f"aggregated_metrics_{timestamp}.json"
            with open(metrics_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(self.aggregated_metrics), f, ensure_ascii=False, indent=2)
            logger.info(f"聚合指标已保存到: {metrics_file}")
        
        # 2. 保存详细评估结果
        if self.evaluation_results:
            eval_file = output_path / f"evaluation_results_{timestamp}.json"
            eval_data = []
            for result in self.evaluation_results:
                eval_data.append(asdict(result))
            
            with open(eval_file, 'w', encoding='utf-8') as f:
                json.dump(eval_data, f, ensure_ascii=False, indent=2)
            logger.info(f"详细评估结果已保存到: {eval_file}")
        
        # 3. 保存查询日志
        if self.query_results:
            query_file = output_path / f"query_log_{timestamp}.json"
            query_data = []
            for result in self.query_results:
                query_data.append({
                    "query": result.query,
                    "execution_time_ms": result.execution_time_ms,
                    "total_results": result.total_results,
                    "file_paths": result.get_file_paths(),
                    "scores": result.get_scores()
                })
            
            with open(query_file, 'w', encoding='utf-8') as f:
                json.dump(query_data, f, ensure_ascii=False, indent=2)
            logger.info(f"查询日志已保存到: {query_file}")
        
        # 4. 生成HTML报告
        self._generate_html_report(output_path, timestamp)
    
    def _generate_html_report(self, output_path: Path, timestamp: str):
        """生成HTML报告"""
        if not self.aggregated_metrics:
            return
        
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>CodebaseQA 评估报告</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .metric {{ margin: 10px 0; }}
        .metric-value {{ font-weight: bold; color: #2196F3; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .success {{ color: #4CAF50; }}
        .warning {{ color: #FF9800; }}
        .error {{ color: #F44336; }}
    </style>
</head>
<body>
    <h1>CodebaseQA 评估报告</h1>
    <p>生成时间: {timestamp}</p>
    
    <h2>总体指标</h2>
    <div class="metric">总查询数: <span class="metric-value">{self.aggregated_metrics.total_queries}</span></div>
    <div class="metric">精确匹配率: <span class="metric-value">{self.aggregated_metrics.exact_match_rate:.3f}</span></div>
    <div class="metric">平均MRR: <span class="metric-value">{self.aggregated_metrics.avg_mrr:.3f}</span></div>
    <div class="metric">平均查询时延: <span class="metric-value">{self.aggregated_metrics.avg_query_time_ms:.2f}ms</span></div>
    
    <h2>详细指标</h2>
    <table>
        <tr><th>指标</th><th>K=1</th><th>K=3</th><th>K=5</th><th>K=10</th></tr>
        <tr>
            <td>Precision@K</td>
            <td>{self.aggregated_metrics.avg_precision_at_k.get(1, 0):.3f}</td>
            <td>{self.aggregated_metrics.avg_precision_at_k.get(3, 0):.3f}</td>
            <td>{self.aggregated_metrics.avg_precision_at_k.get(5, 0):.3f}</td>
            <td>{self.aggregated_metrics.avg_precision_at_k.get(10, 0):.3f}</td>
        </tr>
        <tr>
            <td>Recall@K</td>
            <td>{self.aggregated_metrics.avg_recall_at_k.get(1, 0):.3f}</td>
            <td>{self.aggregated_metrics.avg_recall_at_k.get(3, 0):.3f}</td>
            <td>{self.aggregated_metrics.avg_recall_at_k.get(5, 0):.3f}</td>
            <td>{self.aggregated_metrics.avg_recall_at_k.get(10, 0):.3f}</td>
        </tr>
        <tr>
            <td>F1@K</td>
            <td>{self.aggregated_metrics.avg_f1_at_k.get(1, 0):.3f}</td>
            <td>{self.aggregated_metrics.avg_f1_at_k.get(3, 0):.3f}</td>
            <td>{self.aggregated_metrics.avg_f1_at_k.get(5, 0):.3f}</td>
            <td>{self.aggregated_metrics.avg_f1_at_k.get(10, 0):.3f}</td>
        </tr>
        <tr>
            <td>nDCG@K</td>
            <td>{self.aggregated_metrics.avg_ndcg_at_k.get(1, 0):.3f}</td>
            <td>{self.aggregated_metrics.avg_ndcg_at_k.get(3, 0):.3f}</td>
            <td>{self.aggregated_metrics.avg_ndcg_at_k.get(5, 0):.3f}</td>
            <td>{self.aggregated_metrics.avg_ndcg_at_k.get(10, 0):.3f}</td>
        </tr>
    </table>
</body>
</html>
        """
        
        html_file = output_path / f"evaluation_report_{timestamp}.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"HTML报告已生成: {html_file}")


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    runner = TestRunner(
        test_data_file="dataset/test/arkts_gitee_funCode_summary_highScores.jsonl",
        persist_path="./temp/evaluation_test"
    )
    
    # 运行小规模测试
    try:
        metrics = runner.run_full_evaluation(
            max_queries_per_repo=5,  # 每个仓库最多5个查询
            force_rebuild_index=False,
            repos_to_test=["XmlGraphicsBatik"]  # 只测试一个仓库
        )
        
        # 保存结果
        runner.save_detailed_results("./temp/evaluation_results")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
