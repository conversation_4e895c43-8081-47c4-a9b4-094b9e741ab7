"""
测试数据加载器
负责加载和解析JSONL格式的测试数据文件
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict

logger = logging.getLogger(__name__)


@dataclass
class TestCase:
    """单个测试用例"""
    query: str  # 查询文本 (comment_zh)
    ground_truth_file: str  # 正确答案文件路径 (filepath)
    repo: str  # 仓库名称
    content: str  # 原始代码内容
    functions: List[Dict]  # 函数信息
    token_count: int  # token数量
    is_arkui: bool  # 是否为ArkUI代码
    
    def __post_init__(self):
        """后处理：标准化文件路径"""
        # 确保ground_truth_file是相对路径
        if self.ground_truth_file.startswith('/'):
            # 提取相对于仓库根目录的路径
            parts = self.ground_truth_file.split('/')
            if 'active_repos_gitee_exclude_HarmonyOS_Codelabs' in parts:
                idx = parts.index('active_repos_gitee_exclude_HarmonyOS_Codelabs')
                if idx + 2 < len(parts):  # 确保有repo名称和文件路径
                    self.ground_truth_file = '/'.join(parts[idx + 2:])


@dataclass
class RepoTestData:
    """单个仓库的测试数据"""
    repo_name: str
    repo_path: str
    test_cases: List[TestCase]
    
    def __len__(self):
        return len(self.test_cases)


class TestDataLoader:
    """测试数据加载器"""
    
    def __init__(self, base_repo_path: str = "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs"):
        """
        初始化测试数据加载器
        
        Args:
            base_repo_path: 仓库基础路径
        """
        self.base_repo_path = Path(base_repo_path)
        self.test_data: Dict[str, RepoTestData] = {}
        
    def load_test_data(self, jsonl_file_path: str) -> Dict[str, RepoTestData]:
        """
        加载测试数据文件
        
        Args:
            jsonl_file_path: JSONL测试文件路径
            
        Returns:
            按仓库分组的测试数据
        """
        logger.info(f"开始加载测试数据: {jsonl_file_path}")
        
        jsonl_path = Path(jsonl_file_path)
        if not jsonl_path.exists():
            raise FileNotFoundError(f"测试数据文件不存在: {jsonl_file_path}")
        
        # 按仓库分组收集测试用例
        repo_cases = defaultdict(list)
        total_cases = 0
        
        with open(jsonl_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    test_cases = self._parse_test_case(data)
                    
                    for test_case in test_cases:
                        repo_cases[test_case.repo].append(test_case)
                        total_cases += 1
                        
                except json.JSONDecodeError as e:
                    logger.warning(f"跳过无效JSON行 {line_num}: {e}")
                except Exception as e:
                    logger.warning(f"解析测试用例失败 (行 {line_num}): {e}")
        
        # 构建RepoTestData对象
        for repo_name, cases in repo_cases.items():
            repo_path = self.base_repo_path / repo_name
            self.test_data[repo_name] = RepoTestData(
                repo_name=repo_name,
                repo_path=str(repo_path),
                test_cases=cases
            )
        
        logger.info(f"加载完成: {len(self.test_data)} 个仓库, {total_cases} 个测试用例")
        
        # 打印统计信息
        for repo_name, repo_data in self.test_data.items():
            logger.info(f"  - {repo_name}: {len(repo_data)} 个测试用例")
            
        return self.test_data
    
    def _parse_test_case(self, data: Dict) -> List[TestCase]:
        """
        解析单条JSON数据为测试用例
        
        Args:
            data: JSON数据
            
        Returns:
            测试用例列表（一条数据可能包含多个函数，生成多个测试用例）
        """
        required_fields = ['filepath', 'repo', 'functions']
        for field in required_fields:
            if field not in data:
                raise ValueError(f"缺少必需字段: {field}")
        
        test_cases = []
        
        # 从functions字段中提取查询和ground truth
        functions = data.get('functions', [])
        if not functions:
            logger.warning(f"文件 {data['filepath']} 没有函数信息")
            return test_cases
        
        for func in functions:
            if 'comment_zh' not in func or not func['comment_zh'].strip():
                continue
                
            test_case = TestCase(
                query=func['comment_zh'].strip(),
                ground_truth_file=data['filepath'],
                repo=data['repo'],
                content=data.get('content', ''),
                functions=functions,
                token_count=data.get('token_count', 0),
                is_arkui=data.get('isArkUI', False)
            )
            test_cases.append(test_case)
        
        return test_cases
    
    def get_repo_names(self) -> List[str]:
        """获取所有仓库名称"""
        return list(self.test_data.keys())
    
    def get_repo_data(self, repo_name: str) -> Optional[RepoTestData]:
        """获取指定仓库的测试数据"""
        return self.test_data.get(repo_name)
    
    def get_all_test_cases(self) -> List[Tuple[str, TestCase]]:
        """获取所有测试用例（带仓库名称）"""
        all_cases = []
        for repo_name, repo_data in self.test_data.items():
            for test_case in repo_data.test_cases:
                all_cases.append((repo_name, test_case))
        return all_cases
    
    def validate_repo_paths(self) -> Dict[str, bool]:
        """验证仓库路径是否存在"""
        validation_results = {}
        for repo_name, repo_data in self.test_data.items():
            repo_path = Path(repo_data.repo_path)
            validation_results[repo_name] = repo_path.exists()
            if not repo_path.exists():
                logger.warning(f"仓库路径不存在: {repo_data.repo_path}")
        return validation_results
    
    def get_statistics(self) -> Dict:
        """获取测试数据统计信息"""
        stats = {
            'total_repos': len(self.test_data),
            'total_test_cases': sum(len(repo_data) for repo_data in self.test_data.values()),
            'repos': {}
        }
        
        for repo_name, repo_data in self.test_data.items():
            arkui_count = sum(1 for case in repo_data.test_cases if case.is_arkui)
            stats['repos'][repo_name] = {
                'test_cases': len(repo_data),
                'arkui_cases': arkui_count,
                'non_arkui_cases': len(repo_data) - arkui_count,
                'repo_path': repo_data.repo_path
            }
        
        return stats


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    loader = TestDataLoader()
    test_data_file = "dataset/test/arkts_gitee_funCode_summary_highScores.jsonl"
    
    try:
        test_data = loader.load_test_data(test_data_file)
        stats = loader.get_statistics()
        
        print("=== 测试数据统计 ===")
        print(f"总仓库数: {stats['total_repos']}")
        print(f"总测试用例数: {stats['total_test_cases']}")
        
        print("\n=== 各仓库详情 ===")
        for repo_name, repo_stats in stats['repos'].items():
            print(f"{repo_name}:")
            print(f"  测试用例: {repo_stats['test_cases']}")
            print(f"  ArkUI用例: {repo_stats['arkui_cases']}")
            print(f"  非ArkUI用例: {repo_stats['non_arkui_cases']}")
            print(f"  路径: {repo_stats['repo_path']}")
        
        # 验证路径
        print("\n=== 路径验证 ===")
        validation = loader.validate_repo_paths()
        for repo_name, exists in validation.items():
            status = "✓" if exists else "✗"
            print(f"{status} {repo_name}")
            
    except Exception as e:
        logger.error(f"测试失败: {e}")
