#!/usr/bin/env python3
"""
CodebaseQA 评估工具主启动脚本
"""

import sys
import logging
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from codecompass.evaluation.config import create_evaluation_config, validate_config, print_config
from codecompass.evaluation.test_runner import TestRunner


def setup_logging(log_level: str):
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )


def main():
    """主函数"""
    print("=" * 60)
    print("CodebaseQA 评估工具")
    print("=" * 60)
    
    try:
        # 1. 创建和验证配置
        config = create_evaluation_config()
        
        if not validate_config(config):
            sys.exit(1)
        
        # 2. 设置日志
        setup_logging(config.log_level)
        logger = logging.getLogger(__name__)
        
        # 3. 打印配置
        print_config(config)
        print()
        
        # 4. 确认开始
        if not config.quick_test:
            response = input("是否开始评估？(y/N): ")
            if response.lower() not in ['y', 'yes']:
                print("评估已取消")
                sys.exit(0)
        
        # 5. 创建输出目录
        Path(config.output_dir).mkdir(parents=True, exist_ok=True)
        
        # 6. 初始化测试运行器
        logger.info("初始化测试运行器...")
        runner = TestRunner(
            test_data_file=config.test_data_file,
            base_repo_path=config.base_repo_path,
            persist_path=config.persist_path,
            k_values=config.k_values
        )
        
        # 7. 运行评估
        logger.info("开始运行评估...")
        start_time = time.time()
        
        metrics = runner.run_full_evaluation(
            max_queries_per_repo=config.max_queries_per_repo,
            force_rebuild_index=config.force_rebuild_index,
            repos_to_test=config.repos_to_test
        )
        
        total_time = time.time() - start_time
        
        # 8. 保存结果
        if config.save_detailed_results:
            logger.info("保存详细结果...")
            runner.save_detailed_results(config.output_dir)
        
        # 9. 输出最终结果
        print("\n" + "=" * 60)
        print("评估完成!")
        print("=" * 60)
        print(f"总耗时: {total_time:.2f}秒")
        print(f"总查询数: {metrics.total_queries}")
        print(f"精确匹配率: {metrics.exact_match_rate:.3f}")
        print(f"平均MRR: {metrics.avg_mrr:.3f}")
        print(f"平均查询时延: {metrics.avg_query_time_ms:.2f}ms")
        print(f"最大查询时延: {metrics.max_query_time_ms:.2f}ms")
        
        print("\n主要指标:")
        for k in sorted(metrics.avg_precision_at_k.keys()):
            print(f"  P@{k}: {metrics.avg_precision_at_k[k]:.3f}")
            print(f"  R@{k}: {metrics.avg_recall_at_k[k]:.3f}")
            print(f"  F1@{k}: {metrics.avg_f1_at_k[k]:.3f}")
            print(f"  nDCG@{k}: {metrics.avg_ndcg_at_k[k]:.3f}")
        
        if config.save_detailed_results:
            print(f"\n详细结果已保存到: {config.output_dir}")
        
        print("\n评估成功完成!")
        
    except KeyboardInterrupt:
        print("\n评估被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"评估失败: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
