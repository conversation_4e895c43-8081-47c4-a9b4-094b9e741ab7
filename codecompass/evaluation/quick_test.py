#!/usr/bin/env python3
"""
快速测试脚本
用于验证评估框架是否正常工作
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from codecompass.evaluation.test_runner import TestRunner


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )


def main():
    """主函数"""
    print("=" * 50)
    print("CodebaseQA 快速测试")
    print("=" * 50)
    
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # 配置参数
        test_config = {
            "test_data_file": "dataset/test/arkts_gitee_funCode_summary_highScores.jsonl",
            "base_repo_path": "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs",
            "persist_path": "./temp/evaluation_quick_test",
            "output_dir": "./temp/quick_test_results"
        }
        
        # 检查测试数据文件
        if not Path(test_config["test_data_file"]).exists():
            print(f"错误: 测试数据文件不存在: {test_config['test_data_file']}")
            print("请确保测试数据文件路径正确")
            sys.exit(1)
        
        # 检查仓库路径
        if not Path(test_config["base_repo_path"]).exists():
            print(f"错误: 仓库基础路径不存在: {test_config['base_repo_path']}")
            print("请确保仓库路径正确")
            sys.exit(1)
        
        print("配置信息:")
        for key, value in test_config.items():
            print(f"  {key}: {value}")
        print()
        
        # 创建输出目录
        Path(test_config["output_dir"]).mkdir(parents=True, exist_ok=True)
        
        # 初始化测试运行器
        logger.info("初始化测试运行器...")
        runner = TestRunner(
            test_data_file=test_config["test_data_file"],
            base_repo_path=test_config["base_repo_path"],
            persist_path=test_config["persist_path"],
            k_values=[1, 3, 5, 10]
        )
        
        # 运行快速测试
        logger.info("开始快速测试...")
        print("测试参数:")
        print("  - 测试仓库: XmlGraphicsBatik")
        print("  - 每个仓库最大查询数: 3")
        print("  - 强制重建索引: 否")
        print()
        
        metrics = runner.run_full_evaluation(
            max_queries_per_repo=3,  # 每个仓库最多3个查询
            force_rebuild_index=False,
            repos_to_test=["XmlGraphicsBatik"]  # 只测试一个仓库
        )
        
        # 保存结果
        logger.info("保存测试结果...")
        runner.save_detailed_results(test_config["output_dir"])
        
        # 输出结果摘要
        print("\n" + "=" * 50)
        print("快速测试完成!")
        print("=" * 50)
        print(f"总查询数: {metrics.total_queries}")
        print(f"精确匹配率: {metrics.exact_match_rate:.3f}")
        print(f"平均MRR: {metrics.avg_mrr:.3f}")
        print(f"平均查询时延: {metrics.avg_query_time_ms:.2f}ms")
        
        print("\n主要指标:")
        for k in [1, 3, 5, 10]:
            if k in metrics.avg_precision_at_k:
                print(f"  P@{k}: {metrics.avg_precision_at_k[k]:.3f}")
                print(f"  nDCG@{k}: {metrics.avg_ndcg_at_k[k]:.3f}")
        
        print(f"\n详细结果已保存到: {test_config['output_dir']}")
        print("\n✅ 快速测试成功完成!")
        
        # 给出下一步建议
        print("\n下一步:")
        print("1. 查看详细结果文件")
        print("2. 运行完整评估: python codecompass/evaluation/run_evaluation.py")
        print("3. 或运行指定仓库: python codecompass/evaluation/run_evaluation.py --repos-to-test XmlGraphicsBatik ohos_subsampling_scale_image_view")
        
    except Exception as e:
        logger.error(f"快速测试失败: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        print(f"\n❌ 快速测试失败: {e}")
        print("\n请检查:")
        print("1. 测试数据文件路径是否正确")
        print("2. 仓库基础路径是否正确")
        print("3. 是否有足够的磁盘空间")
        print("4. 网络连接是否正常（用于模型API调用）")
        sys.exit(1)


if __name__ == "__main__":
    main()
