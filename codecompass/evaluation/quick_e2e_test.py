#!/usr/bin/env python3
"""
快速端到端测试脚本
用于验证端到端评测流程是否正常工作
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from codecompass.evaluation.end_to_end_evaluator import EndToEndEvaluator


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )


def main():
    """主函数"""
    print("=" * 60)
    print("CodebaseQA 快速端到端测试")
    print("=" * 60)
    
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # 配置参数
    test_config = {
        "test_file": "dataset/test/testset_lite_3.jsonl",
        "base_repo_path": "/Users/<USER>/ide_dev/data/arkts_from_yyq/active_repos_gitee_exclude_HarmonyOS_Codelabs",
        "venv_path": ".venv"
    }
    
    # 检查文件和路径
    test_file = Path(test_config["test_file"])
    if not test_file.exists():
        print(f"❌ 测试数据文件不存在: {test_config['test_file']}")
        return False
    
    base_repo_path = Path(test_config["base_repo_path"])
    if not base_repo_path.exists():
        print(f"❌ 仓库基础路径不存在: {test_config['base_repo_path']}")
        return False
    
    venv_path = Path(test_config["venv_path"])
    if not venv_path.exists():
        print(f"❌ 虚拟环境路径不存在: {test_config['venv_path']}")
        return False
    
    print("✅ 所有路径检查通过")
    print(f"测试文件: {test_config['test_file']}")
    print(f"仓库路径: {test_config['base_repo_path']}")
    print(f"虚拟环境: {test_config['venv_path']}")
    print()
    
    try:
        # 创建评测器
        evaluator = EndToEndEvaluator(
            base_repo_path=test_config["base_repo_path"],
            venv_path=test_config["venv_path"]
        )
        
        # 运行评测
        logger.info("开始快速端到端测试...")
        summary = evaluator.run_evaluation(test_config["test_file"])
        
        # 输出结果
        print("\n" + "=" * 60)
        print("🎉 快速端到端测试完成!")
        print("=" * 60)
        
        overall = summary['overall_metrics']
        print(f"📊 测试结果:")
        print(f"  总查询数: {overall['total_queries']}")
        print(f"  精确匹配率: {overall['exact_match_rate']:.3f}")
        print(f"  平均MRR: {overall['mrr']:.3f}")
        print(f"  平均查询时延: {overall['avg_query_time_ms']:.2f}ms")
        
        print(f"\n⏱️  性能统计:")
        print(f"  总耗时: {summary['total_time_seconds']:.2f}秒")
        print(f"  成功评测仓库: {summary['successful_repos']}/{summary['total_repos']}")
        
        print(f"\n📈 各仓库表现:")
        for repo, metrics in summary['repo_metrics'].items():
            match_rate = metrics['exact_match_rate']
            status = "✅" if match_rate > 0.5 else "⚠️" if match_rate > 0 else "❌"
            print(f"  {status} {repo}: {match_rate:.3f} ({metrics['exact_match_count']}/{metrics['total_queries']})")
        
        # 保存结果
        import json
        import time
        output_file = f"quick_e2e_test_result_{int(time.time())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细结果已保存到: {output_file}")
        
        print(f"\n🚀 下一步建议:")
        print(f"1. 运行完整评测: python codecompass/evaluation/end_to_end_evaluator.py")
        print(f"2. 使用更大的测试集: --test-file dataset/test/arkts_gitee_funCode_summary_highScores.jsonl")
        print(f"3. 分析结果并优化检索算法")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        return False
    except Exception as e:
        logger.error(f"❌ 快速测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        print(f"\n🔧 故障排除建议:")
        print(f"1. 检查虚拟环境是否正确激活")
        print(f"2. 确保codecompass/app.py可以正常运行")
        print(f"3. 检查网络连接和端口占用")
        print(f"4. 查看详细日志获取更多信息")
        
        return False
    finally:
        # 确保服务被停止
        try:
            evaluator.service_manager.stop_service()
        except:
            pass


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
