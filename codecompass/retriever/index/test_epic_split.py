from utils.epic_split import <PERSON><PERSON><PERSON>litter
from codecompass.config import AppConfig, IgnoreConfig

if __name__ == "__main__":
    from typing import Dict
    import fnmatch
    import os
    import mimetypes
    from llama_index.core import SimpleDirectoryReader
    import sys
    from pathlib import Path
    
    # 添加项目根目录到sys.path以导入配置
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))

    repo_path = "/Users/<USER>/projects/auto-code-rover"

    def file_metadata_func(file_path: str) -> Dict:
        # print(file_path)
        file_path = file_path.replace(repo_path, "")
        if file_path.startswith("/"):
            file_path = file_path[1:]


        # 使用配置中的忽略模式
        ignore_config = IgnoreConfig()
        ignore_patterns = ignore_config.all_ignore_patterns
        category = (
            "test"
            if any(
                fnmatch.fnmatch(file_path, pattern) for pattern in ignore_patterns
            )
            else "implementation"
        )

        return {
            "file_path": file_path,
            "file_name": os.path.basename(file_path),
            "file_type": mimetypes.guess_type(file_path)[0],
            "category": category,
        }

    # 使用配置中的文件扩展名和忽略模式
    ignore_config = IgnoreConfig()
    default_file_extensions = AppConfig().file_extensions
    
    reader = SimpleDirectoryReader(
        input_dir=repo_path,
        exclude=ignore_config.all_ignore_patterns,
        file_metadata=file_metadata_func,
        filename_as_id=True,
        required_exts=default_file_extensions,  # 使用配置而不是硬编码
        recursive=True,
    )
    docs = reader.load_data()
    splitter = EpicSplitter(
        repo_path="/Users/<USER>/projects/auto-code-rover",
    )
    prepared_nodes = splitter.get_nodes_from_documents(docs, show_progress=True)
