from typing import Optional

from .parser import CodeParser
from .python import Python<PERSON>ars<PERSON>
from .typescript import TypescriptParser
from .arkts import ArkTSParser
from .....config import create_config


def is_supported(language: str) -> bool:
    config = create_config()
    return language in config.supported_languages


def create_parser(language: str, **kwargs) -> Optional[CodeParser]:
    if language == "python":
        return PythonParser(**kwargs)
    elif language == "typescript":
        return TypescriptParser(**kwargs)
    elif language == "javascript":
        return TypescriptParser(**kwargs)
    elif language == "arkts":
        return ArkTSParser(**kwargs)

    raise NotImplementedError(f"Language {language} is not supported.")
