import logging
import os
from typing import List, Optional

from llama_index.core import SimpleDirectoryReader
from llama_index.core.schema import Document, TextNode

# 导入配置类型
import sys
from pathlib import Path
current_dir = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(current_dir))
from codecompass.config import IgnoreConfig

# 配置日志
logger = logging.getLogger(__name__)

# 如果没有配置根日志器，则进行基本配置
if not logging.getLogger().handlers:
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(filename)s:%(lineno)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )


class BaseIndexer:
    def __init__(self, persist_path: Optional[str] = None, **kwargs):
        self.persist_path = persist_path
        self.kwargs = kwargs
        self.index = None
        self.repo_path = None

    def load_documents(
        self,
        repo_path: str,
        file_extns: List[str] = None,
        ignore_config: Optional[IgnoreConfig] = None,
    ) -> List[Document]:
        self.repo_path = repo_path

        """
        从代码仓库加载文档

        Args:
            repo_path: 代码仓库路径
            file_extns: 要加载的文件扩展名列表，例如 ['.py', '.js']，如果为None则需要由调用者提供
            ignore_config: 文件忽略配置，如果不提供则使用默认配置

        Returns:
            加载的文档列表
        """
        logger.info(f"开始从仓库加载文档: {repo_path}")
        self.repo_path = repo_path

        if file_extns is None:
            raise ValueError("file_extns参数不能为None，请传入有效的文件扩展名列表")
        
        # 设置默认的忽略配置
        if ignore_config is None:
            ignore_config = IgnoreConfig()
        
        logger.debug(f"使用文件扩展名: {file_extns}")
        logger.debug(f"使用忽略模式: {len(ignore_config.all_ignore_patterns)} 个模式")

        # 使用 SimpleDirectoryReader 加载文档
        try:
            reader = SimpleDirectoryReader(
                input_dir=repo_path,
                recursive=True,
                required_exts=file_extns,
                exclude=ignore_config.all_ignore_patterns,
                filename_as_id=True,
            )
            documents = reader.load_data()
            for i in range(len(documents)):
                documents[i].metadata["file_path"] = (
                    documents[i]
                    .metadata["file_path"]
                    .replace(repo_path, "")
                    .lstrip(os.path.sep)
                )
            logger.info(f"成功加载了 {len(documents)} 个文档")
        except Exception as e:
            logger.error(f"加载文档时出错: {str(e)}")
            raise

        return documents

    def process_documents(self, documents: list) -> List[TextNode]:
        pass

    def build_index(self, nodes: List[TextNode]):
        pass

    def load_index(self):
        pass

    def retrieve(self, query: str, top_k: int = 5) -> List[TextNode]:
        pass
