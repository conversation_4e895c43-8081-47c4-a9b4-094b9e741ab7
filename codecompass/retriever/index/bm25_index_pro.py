import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent.parent.parent))
import fnmatch
import logging
import mimetypes
import os
import hashlib
import pickle
from typing import Dict, List, Optional, Tuple, Set

from llama_index.core import SimpleDirectoryReader

from llama_index.core.node_parser import Sen<PERSON><PERSON><PERSON>plitter
from llama_index.core.schema import Document, TextNode
from llama_index.retrievers.bm25 import BM25Retriever
from pydantic import BaseModel

from codecompass.retriever.index.utils.epic_split import EpicSplitter
from codecompass.retriever.index.utils.file_tracker import create_file_tracker
from codecompass.retriever.index.repo_ops import BaseIndexer, logger
from codecompass.config import IgnoreConfig


class ChunkConfig(BaseModel):
    """
    用于配置代码切片的参数
    """

    chunk_lines: int = 100
    chunk_lines_overlap: int = 15
    max_chars: int = 1500


class EpicChunkConfig(BaseModel):
    min_chunk_size: int = 100
    chunk_size: int = 500
    max_chunk_size: int = 2000
    hard_token_limit: int = 2000
    max_chunks: int = 200


def generate_node_id(node: TextNode) -> str:
    """
    为节点生成唯一标识符
    基于文件路径、起始行、结束行和内容hash
    """
    file_path = node.metadata.get("file_path", "")
    start_line = node.metadata.get("start_line", 0)
    end_line = node.metadata.get("end_line", 0)
    content_hash = hashlib.md5(node.text.encode('utf-8')).hexdigest()[:8]
    
    return f"{file_path}:{start_line}-{end_line}:{content_hash}"


def process_node(doc: Document, doc_node: TextNode) -> TextNode:
    # 计算行号
    return doc_node


def add_line_numbers_to_sentence_nodes(doc: Document, nodes: List[TextNode]) -> List[TextNode]:
    """
    为SentenceSplitter生成的节点添加start_line和end_line信息
    
    Args:
        doc: 原始文档
        nodes: SentenceSplitter生成的节点列表
    
    Returns:
        添加了行号信息的节点列表
    """
    if not nodes:
        return nodes
    
    # 获取文档的全部内容和按行分割
    full_content = doc.text

    # 先过滤掉空节点
    nodes = [node for node in nodes if node.text.strip()]
    
    # 为每个节点计算行号
    line_set = set()
    for node in nodes:

        if node.text in full_content:
            start_char_index = full_content.index(node.text)
            start_line = full_content[:start_char_index].count("\n") + 1
            end_line = start_line + node.text.count("\n")
            while (start_line, end_line) in line_set:
                try:
                    start_char_index = full_content.index(
                        node.text, start_char_index + 1
                    )
                except:
                    continue
                start_line = full_content[:start_char_index].count("\n") + 1
                end_line = start_line + node.text.count("\n")
            line_set.add((start_line, end_line))
            # 添加行号信息到节点元数据
            node.metadata["start_line"] = start_line
            node.metadata["end_line"] = end_line

    return nodes


class BM25IndexPro(BaseIndexer):
    """
    使用 BM25 策略对代码进行切片和索引的简单实现
    """

    def __init__(self, persist_path: Optional[str] = None, ignore_config: Optional[IgnoreConfig] = None, **kwargs):
        """
        初始化 BM25 索引

        Args:
            persist_path: 索引持久化路径，如果提供，将从该路径加载或保存索引
            ignore_config: 文件忽略配置，如果不提供则使用默认配置
        """
        self.repo_path = None
        self.persist_path = persist_path
        self.retriever = None
        self.split_config: ChunkConfig = ChunkConfig(**kwargs)
        self.epic_split_config: EpicChunkConfig = EpicChunkConfig(**kwargs)
        self.ignore_config = ignore_config or IgnoreConfig()

        # 增量构建相关属性
        self._previous_nodes: Dict[str, TextNode] = {}  # 保存之前的节点状态
        self._nodes_state_file = None  # 节点状态文件路径
        self._file_tracker = None  # 文件跟踪器

        if self.persist_path:
            self._nodes_state_file = os.path.join(
                os.path.dirname(self.persist_path), "bm25_nodes_state.pkl"
            )
            # 初始化文件跟踪器
            persist_dir = os.path.dirname(self.persist_path)
            self._file_tracker = create_file_tracker(
                "", persist_dir, "bm25_file_tracker", self.ignore_config
            )

        logger.info(
            f"初始化 BM25IndexPro，持久化路径: {persist_path if persist_path else '未设置'}"
        )
        logger.debug(f"切片配置: {self.split_config}")

    def _save_nodes_state(self, nodes: List[TextNode]) -> None:
        """保存节点状态到文件"""
        if not self._nodes_state_file:
            return

        # 将节点转换为可序列化的格式
        nodes_dict = {}
        for node in nodes:
            node_id = generate_node_id(node)
            # 确保node有唯一的node_id
            if not hasattr(node, "node_id") or not node.node_id:
                import uuid

                node.node_id = str(uuid.uuid4())

            nodes_dict[node_id] = {
                "text": node.text,
                "metadata": node.metadata,
                "node_id": node.node_id,
                "start_char_idx": getattr(node, "start_char_idx", None),
                "end_char_idx": getattr(node, "end_char_idx", None),
            }

        os.makedirs(os.path.dirname(self._nodes_state_file), exist_ok=True)
        with open(self._nodes_state_file, "wb") as f:
            pickle.dump(nodes_dict, f)

        logger.debug(f"节点状态已保存到 {self._nodes_state_file}")

    def _load_nodes_state(self) -> Dict[str, TextNode]:
        """从文件加载节点状态"""
        if not self._nodes_state_file or not os.path.exists(
            self._nodes_state_file
        ):
            return {}

        try:
            with open(self._nodes_state_file, "rb") as f:
                nodes_dict = pickle.load(f)

            # 将字典转换回TextNode对象
            previous_nodes = {}
            for node_id, node_data in nodes_dict.items():
                node = TextNode(
                    text=node_data["text"], metadata=node_data["metadata"]
                )
                # 恢复节点的所有属性
                if node_data.get("node_id"):
                    node.node_id = node_data["node_id"]
                if node_data.get("start_char_idx") is not None:
                    node.start_char_idx = node_data["start_char_idx"]
                if node_data.get("end_char_idx") is not None:
                    node.end_char_idx = node_data["end_char_idx"]

                previous_nodes[node_id] = node

            logger.debug(
                f"从 {self._nodes_state_file} 加载了 {len(previous_nodes)} 个节点状态"
            )
            return previous_nodes

        except Exception as e:
            logger.warning(f"加载节点状态失败: {str(e)}")
            return {}

    def _compute_nodes_diff(
        self, new_nodes: List[TextNode]
    ) -> Tuple[List[TextNode], List[str], List[TextNode]]:
        """
        计算新节点与之前节点的差异

        Returns:
            Tuple[新增节点列表, 删除节点ID列表, 未变化节点列表]
        """
        # 加载之前的节点状态
        previous_nodes = self._load_nodes_state()

        # 为新节点生成ID映射
        new_nodes_dict = {}
        for node in new_nodes:
            node_id = generate_node_id(node)
            new_nodes_dict[node_id] = node

        # 计算差异
        new_node_ids = set(new_nodes_dict.keys())
        previous_node_ids = set(previous_nodes.keys())

        # 新增的节点
        added_node_ids = new_node_ids - previous_node_ids
        added_nodes = [new_nodes_dict[node_id] for node_id in added_node_ids]

        # 删除的节点
        deleted_node_ids = list(previous_node_ids - new_node_ids)

        # 未变化的节点
        unchanged_node_ids = new_node_ids & previous_node_ids
        unchanged_nodes = [
            new_nodes_dict[node_id] for node_id in unchanged_node_ids
        ]

        logger.info(
            f"节点差异分析: 新增 {len(added_nodes)} 个, 删除 {len(deleted_node_ids)} 个, 未变化 {len(unchanged_nodes)} 个"
        )

        return added_nodes, deleted_node_ids, unchanged_nodes

    def load_documents(
        self,
        repo_path: str,
        file_extns: List[str] = None,
    ) -> List[Document]:
        """
        从代码仓库加载文档

        Args:
            repo_path: 代码仓库路径
            file_extns: 要加载的文件扩展名列表，例如 ['.py', '.js']

        Returns:
            加载的文档列表
        """
        self.repo_path = repo_path
        logger.info(f"开始从仓库加载文档: {repo_path}")

        def file_metadata_func(file_path: str) -> Dict:
            # print(file_path)
            file_path = file_path.replace(repo_path, "")
            if file_path.startswith("/"):
                file_path = file_path[1:]

            ignore_patterns = self.ignore_config.all_ignore_patterns
            category = (
                "test"
                if any(
                    fnmatch.fnmatch(file_path, pattern)
                    for pattern in ignore_patterns
                )
                else "implementation"
            )

            ret = {
                "file_path": file_path,
                "file_name": os.path.basename(file_path),
                "file_type": mimetypes.guess_type(file_path)[0],
                "category": category,
            }
            return ret

        if file_extns is None:
            raise ValueError("file_extns参数不能为None，请传入有效的文件扩展名列表")
        
        logger.debug(f"使用文件扩展名: {file_extns}")

        # 使用 SimpleDirectoryReader 加载文档
        try:
            logger.info(f"file_extns: {file_extns}")
            reader = SimpleDirectoryReader(
                input_dir=repo_path,
                recursive=True,
                required_exts=file_extns,
                file_metadata=file_metadata_func,
                filename_as_id=True,
            )
            documents = reader.load_data()
            logger.info(f"成功加载了 {len(documents)} 个文档")
        except Exception as e:
            logger.error(f"加载文档时出错: {str(e)}")
            raise

        return documents

    def process_documents(
        self,
        documents: list,
        incremental: bool = False,
        file_extensions: List[str] = None,
    ) -> List[TextNode]:
        """
        处理文档，将代码切片成节点，支持增量处理

        Args:
            documents: 文档列表
            incremental: 是否启用增量处理
            file_extensions: 支持的文件扩展名

        Returns:
            处理后的节点列表
        """
        logger.info(f"开始处理文档并生成节点 (增量模式: {incremental})")

        if not documents:
            logger.error("没有可处理的文档，请先加载文档")
            raise ValueError("请先加载文档")
        
        if file_extensions is None:
            raise ValueError("file_extensions参数不能为None，请传入有效的文件扩展名列表")

        # 如果不是增量模式，使用原有的处理方式
        if not incremental or not self._file_tracker or not self.repo_path:
            return self._process_all_documents(documents)

        # 增量处理模式 - 只返回新处理的节点，在build_index中进行节点合并
        new_nodes, changed_files, deleted_files = (
            self._process_documents_incremental(documents, file_extensions)
        )

        # 将变化信息存储到实例变量中，供build_index使用
        self._incremental_info = {
            "new_nodes": new_nodes,
            "changed_files": changed_files,
            "deleted_files": deleted_files,
        }

        return new_nodes

    def _process_all_documents(self, documents: list) -> List[TextNode]:
        """处理所有文档（非增量模式）"""
        logger.info("使用全量处理模式")

        splitter = EpicSplitter(
            **self.epic_split_config.model_dump(),
            repo_path=self.repo_path,
        )
        temp_splitter = SentenceSplitter(
            chunk_size=self.split_config.chunk_lines * 16,
            chunk_overlap=self.split_config.chunk_lines_overlap * 16,
            paragraph_separator="\n\n",
        )

        nodes = []
        processed_count = 0

        for doc in documents:
            file_path = doc.metadata.get("file_path", "")
            language = Path(file_path).suffix.lstrip(".")
            logger.debug(f"处理文件: {file_path}, 扩展名: {language}")

            if language in ["py", "python"]:
                language = "python"
            elif language in ["ts", "typescript"]:
                language = "typescript"
            elif language in ["ets", "arkts"]:
                language = "arkts"
            else:
                continue

            splitter.language = language
            logger.debug(f"设置切片器语言为: {language}")

            try:
                doc_nodes = splitter.get_nodes_from_documents([doc])
                if not doc_nodes:
                    doc_nodes = splitter.get_nodes_from_documents([doc])
                doc_nodes = [process_node(doc, node) for node in doc_nodes]
                if len(doc_nodes) == 0:
                    doc_nodes = temp_splitter.get_nodes_from_documents([doc])
                    doc_nodes = add_line_numbers_to_sentence_nodes(
                        doc, doc_nodes
                    )

                # 过滤掉空节点
                valid_doc_nodes = [node for node in doc_nodes if node.text.strip()]
                if len(valid_doc_nodes) != len(doc_nodes):
                    logger.debug(f"文件 {file_path}: 过滤掉 {len(doc_nodes) - len(valid_doc_nodes)} 个空节点")
                
                logger.debug(f"文件 {file_path}: 生成 {len(valid_doc_nodes)} 个有效节点")
                nodes.extend(valid_doc_nodes)
                processed_count += 1
                if processed_count % 10 == 0:
                    logger.info(
                        f"已处理 {processed_count}/{len(documents)} 个文档"
                    )
            except Exception as e:
                logger.warning(
                    f"处理文件 {file_path} 时出错: {str(e)}，跳过此文件"
                )
                continue

        logger.info(f"文档处理完成，共生成 {len(nodes)} 个代码节点")
        return nodes

    def _process_documents_incremental(
        self, documents: list, file_extensions: List[str]
    ) -> Tuple[List[TextNode], List[str], List[str]]:
        """
        增量处理文档

        Returns:
            Tuple[新处理的节点, 修改的文件列表, 删除的文件列表]
        """
        logger.info("使用增量处理模式")

        # 设置文件跟踪器的仓库路径
        self._file_tracker.repo_path = self.repo_path

        # 检测文件变化
        added_files, deleted_files, modified_files = (
            self._file_tracker.detect_file_changes(file_extensions)
        )

        # 为文档创建路径映射
        doc_by_path = {
            doc.metadata.get("file_path", ""): doc for doc in documents
        }

        # 只处理新增和修改的文件
        files_to_process = added_files + modified_files
        new_nodes = []

        if files_to_process:
            logger.info(
                f"需要处理的文件: 新增 {len(added_files)} 个, 修改 {len(modified_files)} 个"
            )

            splitter = EpicSplitter(
                **self.epic_split_config.model_dump(),
                repo_path=self.repo_path,
            )
            temp_splitter = SentenceSplitter(
                chunk_size=self.split_config.chunk_lines * 16,
                chunk_overlap=self.split_config.chunk_lines_overlap * 16,
                paragraph_separator="\n\n",
            )

            processed_count = 0
            for file_path in files_to_process:
                if file_path not in doc_by_path:
                    logger.warning(f"文件 {file_path} 在文档列表中未找到，跳过")
                    continue

                doc = doc_by_path[file_path]
                language = Path(file_path).suffix.lstrip(".")

                if language in ["py", "python"]:
                    language = "python"
                elif language in ["ets", "arkts", "ts", "typescript"]:
                    language = "typescript"
                else:
                    continue

                splitter.language = language

                try:
                    doc_nodes = splitter.get_nodes_from_documents([doc])
                    if not doc_nodes:
                        doc_nodes = splitter.get_nodes_from_documents([doc])
                    doc_nodes = [process_node(doc, node) for node in doc_nodes]
                    if len(doc_nodes) == 0:
                        doc_nodes = temp_splitter.get_nodes_from_documents(
                            [doc]
                        )
                        doc_nodes = add_line_numbers_to_sentence_nodes(
                            doc, doc_nodes
                        )

                    # 过滤掉空节点
                    valid_doc_nodes = [node for node in doc_nodes if node.text.strip()]
                    if len(valid_doc_nodes) != len(doc_nodes):
                        logger.debug(f"文件 {file_path}: 过滤掉 {len(doc_nodes) - len(valid_doc_nodes)} 个空节点")
                    
                    logger.debug(
                        f"文件 {file_path}: 生成 {len(valid_doc_nodes)} 个有效节点"
                    )
                    new_nodes.extend(valid_doc_nodes)
                    processed_count += 1

                    if processed_count % 10 == 0:
                        logger.info(
                            f"已处理 {processed_count}/{len(files_to_process)} 个变化文件"
                        )

                except Exception as e:
                    logger.warning(
                        f"处理文件 {file_path} 时出错: {str(e)}，跳过此文件"
                    )
                    continue

        logger.info(f"增量处理完成: 处理了 {len(new_nodes)} 个新节点")

        # 记录被删除文件的信息
        if deleted_files:
            logger.info(f"检测到 {len(deleted_files)} 个文件被删除")
            for file_path in deleted_files:
                logger.debug(f"删除的文件: {file_path}")

        # 返回新节点和变化信息
        changed_files = modified_files + deleted_files
        return new_nodes, changed_files, deleted_files

    def update_file_tracker_state(self, file_extensions: List[str]) -> None:
        """更新文件跟踪器状态（用于全量重建后同步状态）"""
        if self._file_tracker and self.repo_path:
            self._file_tracker.repo_path = self.repo_path
            self._file_tracker.force_update_state(file_extensions)
            logger.info("文件跟踪器状态已更新")

    def build_index(
        self, nodes: List[TextNode], incremental: bool = False
    ) -> BM25Retriever:
        """
        构建 BM25 索引，支持智能增量更新

        Args:
            nodes: 节点列表
            incremental: 是否为增量更新模式

        Returns:
            BM25Retriever 实例
        """
        logger.info(f"开始构建 BM25 索引 (增量模式: {incremental})")

        # 过滤掉空节点
        if nodes:
            original_count = len(nodes)
            nodes = [node for node in nodes if node.text.strip()]
            if len(nodes) != original_count:
                logger.info(f"过滤掉 {original_count - len(nodes)} 个空节点，剩余 {len(nodes)} 个有效节点")

        if not incremental and not nodes:  # 增量模式仅删除可能没有节点
            logger.error("没有可索引的节点，请先处理文档")
            raise ValueError("请先处理文档")

        # 确保所有节点都有node_id
        for node in nodes:
            if not hasattr(node, "node_id") or not node.node_id:
                import uuid

                node.node_id = str(uuid.uuid4())

        if incremental and self.persist_path:
            # 检查是否有增量信息
            if hasattr(self, "_incremental_info"):
                # 增量更新模式 - 使用文件级别的变化信息
                incremental_info = self._incremental_info
                new_nodes = incremental_info["new_nodes"]
                changed_files = incremental_info["changed_files"]
                deleted_files = incremental_info["deleted_files"]

                if not new_nodes and not changed_files and not deleted_files:
                    logger.info("没有检测到文件变化，跳过索引更新")
                    if self.retriever is None:
                        # 如果当前没有retriever，尝试加载现有索引
                        try:
                            self.load_index()
                        except Exception as e:
                            logger.warning(
                                f"加载现有索引失败: {str(e)}，将重新构建完整索引"
                            )
                            self.retriever = BM25Retriever.from_defaults(
                                nodes=nodes, similarity_top_k=10
                            )
                            # 保存索引和状态
                            if self.persist_path:
                                os.makedirs(
                                    os.path.dirname(self.persist_path),
                                    exist_ok=True,
                                )
                                self.retriever.persist(self.persist_path)
                                self._save_nodes_state(nodes)
                    return self.retriever

                # 加载之前的节点状态
                previous_nodes = self._load_nodes_state()

                # 1. 删除变化文件的旧节点
                old_nodes_to_remove = []
                for file_path in changed_files:
                    old_file_nodes = [
                        node
                        for node_id, node in previous_nodes.items()
                        if node.metadata.get("file_path") == file_path
                    ]
                    old_nodes_to_remove.extend(old_file_nodes)

                # 2. 保留未变化文件的节点
                unchanged_nodes = []
                all_changed_files = set(changed_files)
                for node_id, node in previous_nodes.items():
                    file_path = node.metadata.get("file_path", "")
                    if file_path not in all_changed_files:
                        unchanged_nodes.append(node)

                # 3. 合并最终节点列表
                final_nodes = unchanged_nodes + new_nodes
                
                # 过滤掉空节点
                original_final_count = len(final_nodes)
                final_nodes = [node for node in final_nodes if node.text.strip()]
                if len(final_nodes) != original_final_count:
                    logger.info(f"从最终节点列表中过滤掉 {original_final_count - len(final_nodes)} 个空节点")

                logger.info(
                    f"BM25增量更新: 删除 {len(old_nodes_to_remove)} 个旧节点, "
                    f"保留 {len(unchanged_nodes)} 个未变化节点, "
                    f"新增 {len(new_nodes)} 个节点, 总计 {len(final_nodes)} 个有效节点"
                )

                try:
                    logger.debug(
                        f"重建BM25索引，使用 {len(final_nodes)} 个节点"
                    )
                    self.retriever = BM25Retriever.from_defaults(
                        nodes=final_nodes, similarity_top_k=10
                    )

                    # 保存更新后的索引和节点状态
                    if self.persist_path:
                        os.makedirs(
                            os.path.dirname(self.persist_path), exist_ok=True
                        )
                        self.retriever.persist(self.persist_path)
                        self._save_nodes_state(final_nodes)
                        logger.info(
                            f"增量更新的索引已保存到 {self.persist_path}"
                        )

                except Exception as e:
                    logger.error(f"增量更新索引时出错: {str(e)}")
                    raise
            else:
                # 回退到原有的节点差异分析方法
                logger.info("未找到增量信息，使用节点差异分析进行增量更新")
                added_nodes, deleted_node_ids, unchanged_nodes = (
                    self._compute_nodes_diff(nodes)
                )

                if not added_nodes and not deleted_node_ids:
                    logger.info("没有检测到节点变化，跳过索引更新")
                    if self.retriever is None:
                        try:
                            self.load_index()
                        except Exception as e:
                            logger.warning(
                                f"加载现有索引失败: {str(e)}，将重新构建完整索引"
                            )
                            self.retriever = BM25Retriever.from_defaults(
                                nodes=nodes, similarity_top_k=10
                            )
                            if self.persist_path:
                                os.makedirs(
                                    os.path.dirname(self.persist_path),
                                    exist_ok=True,
                                )
                                self.retriever.persist(self.persist_path)
                                self._save_nodes_state(nodes)
                    return self.retriever

                # 使用原有逻辑重建索引
                current_nodes = unchanged_nodes + added_nodes
                
                # 过滤掉空节点
                original_current_count = len(current_nodes)
                current_nodes = [node for node in current_nodes if node.text.strip()]
                if len(current_nodes) != original_current_count:
                    logger.info(f"从当前节点列表中过滤掉 {original_current_count - len(current_nodes)} 个空节点")
                
                logger.info(
                    f"BM25智能重建: 复用 {len(unchanged_nodes)} 个未变化节点，新增 {len(added_nodes)} 个节点，总计 {len(current_nodes)} 个有效节点"
                )

                try:
                    logger.debug(
                        f"重建BM25索引，使用 {len(current_nodes)} 个节点"
                    )
                    self.retriever = BM25Retriever.from_defaults(
                        nodes=current_nodes, similarity_top_k=10
                    )

                    if self.persist_path:
                        os.makedirs(
                            os.path.dirname(self.persist_path), exist_ok=True
                        )
                        self.retriever.persist(self.persist_path)
                        self._save_nodes_state(nodes)
                        logger.info(
                            f"增量更新的索引已保存到 {self.persist_path}"
                        )

                except Exception as e:
                    logger.error(f"增量更新索引时出错: {str(e)}")
                    raise
        else:
            # 完全重建模式
            try:
                logger.debug(f"完全重建BM25索引，使用 {len(nodes)} 个节点")
                self.retriever = BM25Retriever.from_defaults(
                    nodes=nodes, similarity_top_k=10
                )

                # 保存索引和节点状态
                if self.persist_path:
                    os.makedirs(
                        os.path.dirname(self.persist_path), exist_ok=True
                    )
                    self.retriever.persist(self.persist_path)
                    self._save_nodes_state(nodes)
                    logger.info(f"索引已成功保存到 {self.persist_path}")
                else:
                    logger.warning("未提供持久化路径，索引不会被保存")

            except Exception as e:
                logger.error(f"构建索引时出错: {str(e)}")
                raise

        return self.retriever

    def load_index(self) -> BM25Retriever:
        """
        从持久化路径加载索引

        Returns:
            BM25Retriever 实例
        """
        logger.info(f"尝试从 {self.persist_path} 加载索引")

        if not self.persist_path:
            logger.error("未提供持久化路径，无法加载索引")
            raise ValueError("未提供持久化路径")

        try:
            self.retriever = BM25Retriever.from_persist_dir(self.persist_path)
            logger.info(f"成功从 {self.persist_path} 加载了索引")
        except Exception as e:
            logger.error(f"加载索引时出错: {str(e)}")
            raise

        return self.retriever

    def _filter_nodes_by_path(self, nodes: List[TextNode], include: List[str] = None, exclude: List[str] = None) -> List[TextNode]:
        """
        根据include和exclude模式过滤节点
        
        Args:
            nodes: 节点列表
            include: 包含路径模式列表，为空时不过滤
            exclude: 排除路径模式列表，为空时不过滤
        
        Returns:
            过滤后的节点列表
        """
        if not nodes:
            return nodes
        
        filtered_nodes = []
        
        for node in nodes:
            file_path = node.metadata.get('file_path', '')
            
            # 处理include逻辑
            if include:
                # 如果有include模式，只保留匹配的文件
                include_match = False
                for pattern in include:
                    if fnmatch.fnmatch(file_path, pattern):
                        include_match = True
                        break
                if not include_match:
                    continue  # 不匹配include模式，跳过
            
            # 处理exclude逻辑
            if exclude:
                # 如果有exclude模式，排除匹配的文件
                exclude_match = False
                for pattern in exclude:
                    if fnmatch.fnmatch(file_path, pattern):
                        exclude_match = True
                        break
                if exclude_match:
                    continue  # 匹配exclude模式，跳过
            
            # 通过所有过滤条件，保留该节点
            filtered_nodes.append(node)
        
        return filtered_nodes

    def retrieve(
        self, query: str, top_k: int = 5, threshold: float = 0.5, 
        include: List[str] = None, exclude: List[str] = None
    ) -> List[TextNode]:
        """
        检索与查询相关的代码片段

        Args:
            query: 查询字符串
            top_k: 返回的结果数量
            threshold: 相关度阈值
            include: 包含路径模式列表，为空时不限制
            exclude: 排除路径模式列表，为空时不排除

        Returns:
            检索到的节点列表
        """
        logger.info(f"执行检索查询: '{query}'，top_k={top_k}")

        if not self.retriever:
            logger.error("检索器未初始化，请先构建或加载索引")
            raise ValueError("请先构建或加载索引")

        # 执行检索 - 修改这里，移除 similarity_top_k 参数
        # 使用 BM25Retriever 的标准参数
        try:
            retrieved_nodes = self.retriever.retrieve(query)
            logger.debug(f"检索到 {len(retrieved_nodes)} 个原始结果")

            # 立即进行路径过滤，避免后续无效处理
            if include or exclude:
                original_count = len(retrieved_nodes)
                retrieved_nodes = self._filter_nodes_by_path(retrieved_nodes, include, exclude)
                logger.debug(f"路径过滤：从 {original_count} 个结果过滤为 {len(retrieved_nodes)} 个结果")

            # 排序和过滤结果
            retrieved_nodes = sorted(
                retrieved_nodes, key=lambda x: x.score, reverse=True
            )
            retrieved_nodes = [
                node
                for node in retrieved_nodes
                if hasattr(node, "score") and node.score >= threshold
            ]
            for node in retrieved_nodes:
                node.score = node.score * 0.12 # bm25与embedding分数差异均衡经验值
            retrieved_nodes = retrieved_nodes[:top_k]

            # 记录检索结果的基本信息
            for i, node in enumerate(retrieved_nodes):
                file_path = node.metadata.get("file_path", "Unknown")
                score = node.score if hasattr(node, "score") else "N/A"
                logger.debug(f"结果 {i + 1}: 文件={file_path}, 相关度={score}")

        except Exception as e:
            logger.error(f"执行检索时出错: {str(e)}")
            raise

        return retrieved_nodes


def build_bm25_index(
    repo_path: str,
    persist_path: str,
    file_extensions: list = None,
    force: bool = False,
    incremental: bool = False,
    ignore_config: Optional[IgnoreConfig] = None,
    **config_kwargs,
) -> BM25IndexPro:
    """
    构建BM25索引的主函数

    Args:
        repo_path: 代码仓库路径
        persist_path: 索引持久化路径
        file_extensions: 支持的文件扩展名列表
        force: 是否强制重建索引
        incremental: 是否启用增量构建模式
        ignore_config: 文件忽略配置，如果不提供则使用默认配置
        **config_kwargs: 其他配置参数

    Returns:
        BM25IndexPro实例
    """
    logger.info(
        f"开始构建BM25索引: {repo_path} -> {persist_path} (增量模式: {incremental})"
    )
    
    if file_extensions is None:
        raise ValueError("file_extensions参数不能为None，请传入有效的文件扩展名列表")

    index = BM25IndexPro(persist_path=persist_path, ignore_config=ignore_config, **config_kwargs)

    # 构建索引
    if not force and not incremental and os.path.exists(persist_path):
        logger.info("检测到现有索引，将加载现有索引")
        index.load_index()
    else:
        if incremental:
            logger.info("启用增量构建模式")
        else:
            logger.info("将构建新索引")
        documents = index.load_documents(
            repo_path=repo_path, file_extns=file_extensions
        )
        nodes = index.process_documents(
            documents, incremental=incremental, file_extensions=file_extensions
        )
        index.build_index(nodes, incremental=incremental)

        # 无论是否增量模式，都需要更新文件跟踪器状态以保持同步
        index.update_file_tracker_state(file_extensions)

    return index


def main():
    """兼容命令行调用的主函数"""
    import argparse

    parser = argparse.ArgumentParser(
        description="使用 BM25 策略对代码进行切片和索引的简单实现"
    )
    parser.add_argument(
        "--repo_path",
        type=str,
        required=True,
        help="代码仓库路径",
    )
    parser.add_argument(
        "--persist_path",
        type=str,
        required=True,
        help="索引持久化路径",
    )
    parser.add_argument(
        "--chunk_lines",
        type=int,
        default=100,
        help="切片的行数",
    )
    parser.add_argument(
        "--chunk_lines_overlap",
        type=int,
        default=15,
        help="切片的重叠行数",
    )
    parser.add_argument(
        "--max_chars",
        type=int,
        default=1500,
        help="切片的最大字符数",
    )

    args = parser.parse_args()

    config_kwargs = {
        "chunk_lines": args.chunk_lines,
        "chunk_lines_overlap": args.chunk_lines_overlap,
        "max_chars": args.max_chars,
    }

    # 使用默认忽略配置
    ignore_config = IgnoreConfig()
    build_bm25_index(args.repo_path, args.persist_path, ignore_config=ignore_config, **config_kwargs)


# 使用示例
def example():
    # 配置日志
    logging.basicConfig(
        level=logging.DEBUG,
        format="%(asctime)s - %(filename)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    logger.info("开始执行 BM25IndexPro 示例")

    # 初始化 BM25 索引
    repo_path = "/Users/<USER>/projects/hmos_projects/hmosworld"
    # repo_path = "/Users/<USER>/ide_dev/repo/CodebaseQA/example_repos/TouristParkDemo"
    repo_name = os.path.basename(repo_path)
    persist_path = f"/Users/<USER>/projects/code_qa/codecompass/temp/bm25_index_pro/{repo_name}"

    logger.info(f"使用仓库: {repo_path}")
    logger.info(f"索引持久化路径: {persist_path}")

    # 使用默认的忽略配置
    ignore_config = IgnoreConfig()
    index = BM25IndexPro(persist_path=persist_path, ignore_config=ignore_config)

    # 构建索引
    if os.path.exists(persist_path):
        logger.info("检测到现有索引，将加载现有索引")
        index.load_index()
    else:
        logger.info("未检测到现有索引，将构建新索引")
        documents = index.load_documents(repo_path=repo_path)
        nodes = index.process_documents(documents)
        index.build_index(nodes)

    # 检索示例
    # query = "查找文件读取相关的代码, 比如hello world"
    # query = "persisted_test_user"
    query = "TabBarData"
    logger.info(f"执行示例查询: '{query}'")
    results = index.retrieve(query)

    # 打印结果
    logger.info(f"共检索到 {len(results)} 个结果")
    for i, node in enumerate(results):
        start_line = node.metadata.get("start_line", "Unknown")
        end_line = node.metadata.get("end_line", "Unknown")
        logger.info(f"结果 {i + 1}:".center(30, "-"))
        logger.info(
            f"文件: {node.metadata.get('file_path', 'Unknown')}:({start_line}-{end_line})"
        )
        logger.info(
            f"相关度: {node.score if hasattr(node, 'score') else 'N/A'}"
        )
        logger.info(f"内容: {node.text[:200]}...")

    logger.info("示例执行完成")


if __name__ == "__main__":
    example()
    # main()
