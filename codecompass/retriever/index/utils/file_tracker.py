import os
import hashlib
import pickle
import logging
import fnmatch
from typing import Dict, List, Tuple, Set, Optional
from pathlib import Path

# 导入配置类型
import sys
current_dir = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(current_dir))
from codecompass.config import IgnoreConfig

logger = logging.getLogger(__name__)


class FileTracker:
    """文件级别的变化跟踪器，用于检测文件的新增、删除和修改"""
    
    def __init__(self, repo_path: str, state_file: str, ignore_config: Optional[IgnoreConfig] = None):
        """
        初始化文件跟踪器
        
        Args:
            repo_path: 代码仓库路径
            state_file: 保存文件状态的文件路径
            ignore_config: 文件忽略配置，如果不提供则使用默认配置
        """
        self.repo_path = repo_path
        self.state_file = state_file
        self.ignore_config = ignore_config or IgnoreConfig()
        self._previous_files: Dict[str, str] = {}  # 文件路径 -> CRC32
        self._load_file_state()
    
    def _compute_file_crc32(self, file_path: str) -> str:
        """计算单个文件的CRC32校验和"""
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
                return hashlib.md5(content).hexdigest()
        except Exception as e:
            logger.warning(f"计算文件CRC32失败 {file_path}: {str(e)}")
            return ""
    
    def _should_ignore_file(self, relative_path: str) -> bool:
        """检查文件是否应该被忽略"""
        ignore_patterns = self.ignore_config.all_ignore_patterns
        for pattern in ignore_patterns:
            if fnmatch.fnmatch(relative_path, pattern):
                return True
        return False
    
    def _scan_current_files(self, file_extensions: List[str]) -> Dict[str, str]:
        """扫描当前仓库中的所有文件并计算CRC32"""
        current_files = {}
        
        for ext in file_extensions:
            for file_path in Path(self.repo_path).rglob(f"*{ext}"):
                if file_path.is_file():
                    relative_path = str(file_path.relative_to(self.repo_path))
                    
                    # 检查是否应该忽略此文件
                    if self._should_ignore_file(relative_path):
                        continue
                    
                    crc32 = self._compute_file_crc32(str(file_path))
                    if crc32:  # 只有成功计算CRC32的文件才加入
                        current_files[relative_path] = crc32
        
        logger.info(f"扫描到 {len(current_files)} 个文件")
        return current_files
    
    def _load_file_state(self) -> None:
        """从文件加载之前的文件状态"""
        if not os.path.exists(self.state_file):
            logger.info(f"文件状态文件不存在，初始化为空: {self.state_file}")
            return
        
        try:
            with open(self.state_file, 'rb') as f:
                self._previous_files = pickle.load(f)
            logger.info(f"加载了 {len(self._previous_files)} 个文件的历史状态")
        except Exception as e:
            logger.warning(f"加载文件状态失败: {str(e)}")
            self._previous_files = {}
    
    def _save_file_state(self, current_files: Dict[str, str]) -> None:
        """保存当前文件状态到文件"""
        try:
            os.makedirs(os.path.dirname(self.state_file), exist_ok=True)
            with open(self.state_file, 'wb') as f:
                pickle.dump(current_files, f)
            logger.debug(f"文件状态已保存到 {self.state_file}")
        except Exception as e:
            logger.error(f"保存文件状态失败: {str(e)}")
    
    def detect_file_changes(self, file_extensions: List[str]) -> Tuple[List[str], List[str], List[str]]:
        """
        检测文件变化
        
        Args:
            file_extensions: 要检测的文件扩展名列表
            
        Returns:
            Tuple[新增文件列表, 删除文件列表, 修改文件列表]
        """
        logger.info("开始检测文件变化...")
        
        # 扫描当前文件
        current_files = self._scan_current_files(file_extensions)
        
        # 计算差异
        current_file_set = set(current_files.keys())
        previous_file_set = set(self._previous_files.keys())
        
        # 新增的文件
        added_files = list(current_file_set - previous_file_set)
        
        # 删除的文件
        deleted_files = list(previous_file_set - current_file_set)
        
        # 修改的文件 (存在于两个集合中但CRC32不同)
        common_files = current_file_set & previous_file_set
        modified_files = [
            file_path for file_path in common_files
            if current_files[file_path] != self._previous_files[file_path]
        ]
        
        logger.info(f"文件变化检测完成: 新增 {len(added_files)} 个, 删除 {len(deleted_files)} 个, 修改 {len(modified_files)} 个")
        
        # 保存当前状态
        self._save_file_state(current_files)
        self._previous_files = current_files
        
        return added_files, deleted_files, modified_files
    
    def get_unchanged_files(self, file_extensions: List[str]) -> List[str]:
        """获取未变化的文件列表"""
        current_files = self._scan_current_files(file_extensions)
        
        current_file_set = set(current_files.keys())
        previous_file_set = set(self._previous_files.keys())
        
        # 未变化的文件 (存在于两个集合中且CRC32相同)
        common_files = current_file_set & previous_file_set
        unchanged_files = [
            file_path for file_path in common_files
            if current_files[file_path] == self._previous_files[file_path]
        ]
        
        return unchanged_files
    
    def force_update_state(self, file_extensions: List[str]) -> None:
        """强制更新文件状态（用于全量重建后）"""
        logger.info("强制更新文件状态...")
        current_files = self._scan_current_files(file_extensions)
        self._save_file_state(current_files)
        self._previous_files = current_files
        logger.info(f"已更新 {len(current_files)} 个文件的状态")


def create_file_tracker(repo_path: str, persist_dir: str, tracker_name: str = "file_tracker", ignore_config: Optional[IgnoreConfig] = None) -> FileTracker:
    """
    创建文件跟踪器实例的便捷函数
    
    Args:
        repo_path: 代码仓库路径
        persist_dir: 持久化目录
        tracker_name: 跟踪器名称（用于生成状态文件名）
        ignore_config: 文件忽略配置，如果不提供则使用默认配置
        
    Returns:
        FileTracker实例
    """
    state_file = os.path.join(persist_dir, f"{tracker_name}_state.pkl")
    return FileTracker(repo_path, state_file, ignore_config) 