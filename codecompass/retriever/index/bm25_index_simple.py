import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent.parent.parent))
import logging
import os
from typing import List, Optional

from llama_index.core.node_parser import CodeSplitter
from llama_index.core.schema import Document, TextNode
from llama_index.retrievers.bm25 import BM25Retriever
from pydantic import BaseModel

from .repo_ops import BaseIndexer, logger


class ChunkConfig(BaseModel):
    """
    用于配置代码切片的参数
    """

    chunk_lines: int = 100
    chunk_lines_overlap: int = 15
    max_chars: int = 1500


def process_node(doc: Document, doc_node: TextNode):
    doc_str = doc.text
    star_char_idx = doc_node.start_char_idx
    end_char_idx = doc_node.end_char_idx
    # 计算行号
    start_line = len(doc_str[:star_char_idx].splitlines())
    end_line = len(doc_str[:end_char_idx].splitlines())
    doc_node.metadata["start_line"] = start_line
    doc_node.metadata["end_line"] = end_line
    return doc_node


class BM25IndexSimple(BaseIndexer):
    """
    使用 BM25 策略对代码进行切片和索引的简单实现
    """

    def __init__(self, persist_path: Optional[str] = None, **kwargs):
        """
        初始化 BM25 索引

        Args:
            persist_path: 索引持久化路径，如果提供，将从该路径加载或保存索引
        """
        self.persist_path = persist_path
        self.retriever = None
        self.split_config: ChunkConfig = ChunkConfig(**kwargs)
        logger.info(
            f"初始化 BM25IndexSimple，持久化路径: {persist_path if persist_path else '未设置'}"
        )
        logger.debug(f"切片配置: {self.split_config}")

    def process_documents(self, documents: list) -> List[TextNode]:
        """
        处理文档，将代码切片成节点

        Returns:
            处理后的节点列表
        """
        logger.info("开始处理文档并生成节点")

        if not documents:
            logger.error("没有可处理的文档，请先加载文档")
            raise ValueError("请先加载文档")

        # 使用 CodeSplitter 对代码进行切片
        # 可以根据需要调整参数
        splitter = CodeSplitter(
            language="python",  # 可以根据文件类型动态设置
            **self.split_config.model_dump(),
        )
        logger.debug(f"创建了CodeSplitter，初始语言设置为python")

        # 处理所有文档
        nodes = []
        processed_count = 0

        for doc in documents:
            # 获取文件扩展名来确定语言
            file_path = doc.metadata.get("file_path", "")
            language = Path(file_path).suffix.lstrip(".")
            logger.debug(f"处理文件: {file_path}, 扩展名: {language}")

            if language in ["py", "python"]:
                language = "python"
            elif language in ["arkts", "ts", "typescript", "ets"]:
                language = "typescript"
            else:
                continue

            # 使用适当的语言设置切片器
            splitter.language = language
            logger.debug(f"设置切片器语言为: {language}")

            # 切片文档
            try:
                doc_nodes = splitter.get_nodes_from_documents([doc])
                doc_nodes = [process_node(doc, node) for node in doc_nodes]
                nodes.extend(doc_nodes)
                processed_count += 1
                if processed_count % 10 == 0:  # 每处理10个文档记录一次进度
                    logger.info(
                        f"已处理 {processed_count}/{len(documents)} 个文档"
                    )
            except Exception as e:
                logger.warning(
                    f"处理文件 {file_path} 时出错: {str(e)}，跳过此文件"
                )
                continue

        logger.info(f"文档处理完成，共生成 {len(nodes)} 个代码节点")
        return nodes

    def build_index(self, nodes: List[TextNode]) -> BM25Retriever:
        """
        构建 BM25 索引

        Returns:
            BM25Retriever 实例
        """
        logger.info("开始构建 BM25 索引")

        if not nodes:
            logger.error("没有可索引的节点，请先处理文档")
            raise ValueError("请先处理文档")

        # 创建 BM25 检索器
        try:
            logger.debug(f"使用 {len(nodes)} 个节点创建 BM25Retriever")
            self.retriever = BM25Retriever.from_defaults(
                nodes=nodes,
                similarity_top_k=10,  # 返回的最相关结果数量
            )

            # 如果提供了持久化路径，保存索引
            if self.persist_path:
                os.makedirs(os.path.dirname(self.persist_path), exist_ok=True)
                self.retriever.persist(self.persist_path)
                logger.info(f"索引已成功保存到 {self.persist_path}")
            else:
                logger.warning("未提供持久化路径，索引不会被保存")

        except Exception as e:
            logger.error(f"构建索引时出错: {str(e)}")
            raise

        return self.retriever

    def load_index(self) -> BM25Retriever:
        """
        从持久化路径加载索引

        Returns:
            BM25Retriever 实例
        """
        logger.info(f"尝试从 {self.persist_path} 加载索引")

        if not self.persist_path:
            logger.error("未提供持久化路径，无法加载索引")
            raise ValueError("未提供持久化路径")

        try:
            self.retriever = BM25Retriever.from_persist_dir(self.persist_path)
            logger.info(f"成功从 {self.persist_path} 加载了索引")
        except Exception as e:
            logger.error(f"加载索引时出错: {str(e)}")
            raise

        return self.retriever

    def retrieve(self, query: str, top_k: int = 5, include: List[str] = None, exclude: List[str] = None) -> List[TextNode]:
        """
        检索与查询相关的代码片段

        Args:
            query: 查询字符串
            top_k: 返回的结果数量
            include: 包含路径模式列表（简单版本不支持，保持兼容性）
            exclude: 排除路径模式列表（简单版本不支持，保持兼容性）

        Returns:
            检索到的节点列表
        """
        logger.info(f"执行检索查询: '{query}'，top_k={top_k}")

        if not self.retriever:
            logger.error("检索器未初始化，请先构建或加载索引")
            raise ValueError("请先构建或加载索引")

        # 执行检索 - 修改这里，移除 similarity_top_k 参数
        # 使用 BM25Retriever 的标准参数
        try:
            retrieved_nodes = self.retriever.retrieve(query)
            logger.debug(f"检索到 {len(retrieved_nodes)} 个原始结果")

            # 如果需要限制结果数量，可以在这里手动截取
            if len(retrieved_nodes) > top_k:
                retrieved_nodes = retrieved_nodes[:top_k]
                logger.debug(f"结果已截取为前 {top_k} 个")

            retrieved_nodes = [node for node in retrieved_nodes if hasattr(node, "score") and node.score > 0.01]

            # 记录检索结果的基本信息
            for i, node in enumerate(retrieved_nodes):
                file_path = node.metadata.get("file_path", "Unknown")
                score = node.score if hasattr(node, "score") else "N/A"
                logger.debug(f"结果 {i + 1}: 文件={file_path}, 相关度={score}")

        except Exception as e:
            logger.error(f"执行检索时出错: {str(e)}")
            raise

        return retrieved_nodes


def main():
    import argparse

    parser = argparse.ArgumentParser(
        description="使用 BM25 策略对代码进行切片和索引的简单实现"
    )
    parser.add_argument(
        "--repo_path",
        type=str,
        required=True,
        help="代码仓库路径",
    )
    parser.add_argument(
        "--persist_path",
        type=str,
        required=True,
        help="索引持久化路径",
    )
    parser.add_argument(
        "--chunk_lines",
        type=int,
        default=100,
        help="切片的行数",
    )
    parser.add_argument(
        "--chunk_lines_overlap",
        type=int,
        default=15,
        help="切片的重叠行数",
    )
    parser.add_argument(
        "--max_chars",
        type=int,
        default=1500,
        help="切片的最大字符数",
    )

    args = parser.parse_args()
    index = BM25IndexSimple(
        persist_path=args.persist_path,
        chunk_lines=args.chunk_lines,
        chunk_lines_overlap=args.chunk_lines_overlap,
        max_chars=args.max_chars,
    )
    # 构建索引
    if os.path.exists(args.persist_path):
        logger.info("检测到现有索引，将加载现有索引")
        index.load_index()
    else:
        logger.info("未检测到现有索引，将构建新索引")
        documents = index.load_documents(repo_path=args.repo_path)
        nodes = index.process_documents(documents)
        index.build_index(nodes)


# 使用示例
def example():
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(filename)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    logger.info("开始执行 BM25IndexSimple 示例")

    # 初始化 BM25 索引
    repo_path = (
        "/Users/<USER>/projects/code_qa/test/locAgent/playground/chainlit"
    )
    repo_name = os.path.basename(repo_path)
    persist_path = f"/Users/<USER>/projects/code_qa/codecompass/temp/bm25_index_simple/{repo_name}"

    logger.info(f"使用仓库: {repo_path}")
    logger.info(f"索引持久化路径: {persist_path}")

    index = BM25IndexSimple(persist_path=persist_path)

    # 构建索引
    if os.path.exists(persist_path):
        logger.info("检测到现有索引，将加载现有索引")
        index.load_index()
    else:
        logger.info("未检测到现有索引，将构建新索引")
        documents = index.load_documents(repo_path=repo_path)
        nodes = index.process_documents(documents)
        index.build_index(nodes)

    # 检索示例
    # query = "查找文件读取相关的代码, 比如hello world"
    query = "聊天接口"
    logger.info(f"执行示例查询: '{query}'")
    results = index.retrieve(query)

    # 打印结果
    logger.info(f"共检索到 {len(results)} 个结果")
    for i, node in enumerate(results):
        logger.info(f"\n结果 {i + 1}:")
        logger.info(f"文件: {node.metadata.get('file_path', 'Unknown')}")
        logger.info(
            f"相关度: {node.score if hasattr(node, 'score') else 'N/A'}"
        )
        logger.info(f"内容: {node.text[:200]}...")

    logger.info("示例执行完成")


if __name__ == "__main__":
    example()
    # main()
