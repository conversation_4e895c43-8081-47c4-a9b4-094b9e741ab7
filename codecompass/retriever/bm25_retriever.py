import os
from .index import BM25<PERSON>ndexSimple, BM25IndexPro
from abc import ABC
from typing import List, Optional
from codecompass.config import IgnoreConfig


class BM25Retriever(ABC):
    def __init__(self, persist_path: str) -> None:
        self.persist_path = persist_path
        self.index = None

    def pre_build(self, repo_path):
        documents = self.index.load_documents(repo_path)
        nodes = self.index.process_documents(documents)
        self.index.build_index(nodes)

    def retrieve(self, query, top_k=5, include: List[str] = None, exclude: List[str] = None) -> list:
        return self.index.retrieve(query, top_k, include=include, exclude=exclude)


class BM25RetrieverSimple(BM25Retriever):
    def __init__(self, persist_path: str) -> None:
        super().__init__(persist_path)
        self.index = BM25IndexSimple(persist_path)
        if os.path.exists(self.persist_path):
            self.index.load_index()


class BM25RetrieverPro(BM25Retriever):
    def __init__(self, persist_path: str, ignore_config: Optional[IgnoreConfig] = None) -> None:
        super().__init__(persist_path)
        self.index = BM25IndexPro(persist_path, ignore_config=ignore_config)
        if os.path.exists(self.persist_path):
            self.index.load_index()
