from typing import Callable
from io import Text<PERSON>Wrapper
from charset_normalizer import from_bytes

READLINES = TextIOWrapper.readlines
READ = TextIOWrapper.read
DEFAULT_OPER_MAP = {
    READ: "",
    READLINES: [],
}

def read_file(file_path: str, pri_encoding: str = "", oper: Callable = READ):
    if pri_encoding:
        with open(file_path, "r", encoding=pri_encoding) as f:
            return oper(f)
    if oper not in DEFAULT_OPER_MAP:
        raise ValueError(f"Unsupported read file operation: {oper}")
    for encoding in ["utf-8", "gbk", "gb2312"]:
        try:
            with open(file_path, "r", encoding=encoding) as f:
                return oper(f)
        except:
            continue
    with open(file_path, 'rb') as f:
        byte_content = f.read()
        if not byte_content:
            return DEFAULT_OPER_MAP[oper]
        result = from_bytes(byte_content)
        if result:
            detected_encoding = result.best().encoding
            with open(file_path, "r", encoding=detected_encoding) as f:
                return oper(f)
    print(f"read_file: {file_path} failed!")
    return DEFAULT_OPER_MAP[oper]