from platform import system as platform_system, machine as platform_machine


def get_system_and_arch():
    system = platform_system().lower()
    if system == "windows":
        system = "windows"
    elif system == "darwin":
        system = "mac"
    else:
        system = "linux"
    arch = platform_machine().lower()
    if arch in ["amd64", "x86_64"]:
        cpu_arch = "x64"
    elif arch in ["arm64", "aarch64"]:
        cpu_arch = "arm64"
    else:
        cpu_arch = "x86"
    return system, cpu_arch