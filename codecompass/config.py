import os
import argparse
from typing import List
from pydantic import BaseModel



class IndexConfig(BaseModel):
    """索引配置"""
    chunk_lines: int = 100
    chunk_lines_overlap: int = 15
    max_chars: int = 1500
    min_chunk_size: int = 100
    chunk_size: int = 500
    max_chunk_size: int = 2000
    hard_token_limit: int = 2000
    max_chunks: int = 200
    refresh_interval: int = 60  # 索引刷新间隔(秒)
    enable_auto_refresh: bool = True  # 是否启用自动刷新
    enable_code_insights: bool = True  # 是否启用code insights生成


class EmbeddingConfig(BaseModel):
    """Embedding配置"""
    model_config = {'protected_namespaces': ()}

    model_name: str = "Qwen/Qwen3-Embedding-0.6B"
    api_base: str = "https://api.siliconflow.cn/v1"
    api_key: str = "sk-ravccrfhdceccblziftcgyzodzjbftdzrozbofbxrhayptnd"
    embed_batch_size: int = 10


class LLMConfig(BaseModel):
    """LLM配置"""
    model_config = {'protected_namespaces': ()}

    model_name: str = "doubao-seed-1-6-250615"
    api_base: str = "https://ark.cn-beijing.volces.com/api/v3"
    api_key: str = "ba1a55eb-f6ad-4bd0-88fb-4e3ab5f69722"


class RerankConfig(BaseModel):
    """Rerank配置"""
    model_config = {'protected_namespaces': ()}

    model_name: str = "Qwen/Qwen3-Reranker-0.6B"
    api_base: str = "https://api.siliconflow.cn/v1"
    api_key: str = "sk-ravccrfhdceccblziftcgyzodzjbftdzrozbofbxrhayptnd"


class IgnoreConfig(BaseModel):
    """文件和目录忽略配置"""

    # 隐藏目录
    hidden_directories: List[str] = [
        ".*",
        ".*/**",
        "**/.*",
    ]

    # 开发和构建相关目录
    ignore_directories: List[str] = [
        "**/__pycache__/**",
        "**/.pytest_cache/**",
        "**/venv/**",
        "**/node_modules/**",
        "**/dist/**",
        "**/build/**",
        "**/cache/**",
        "**/logs/**",
        "**/temp/**",
        "**/codebase_cache/**",
        "**/oh_modules/**",
    ]
    
    # 测试相关
    ignore_test_patterns: List[str] = [
        # "**/test/**",
        # "**/tests/**",
        # "**/test_*.*",
        # "**/*_test.*",
    ]
    
    # 版本控制和IDE
    ignore_vcs_ide: List[str] = [
        "*.git*",
        "*.vscode*",
        "*.venv*",
        "*.egg-info*",
    ]
    
    # 日志和覆盖率
    ignore_logs_coverage: List[str] = [
        "**/.coverage/**",
        "**/.coverage.*",
        "*.log",
        "*.log.*",
        "*logs",
    ]
    
    # 缓存和临时文件
    ignore_cache_temp: List[str] = [
        "*__pycache__*",
        "*.pytest_cache*",
        "*.cache",
        "*.pyc",
    ]
    
    # 媒体和数据文件
    ignore_media_data: List[str] = [
        "*.png",
        "*.jpeg",
        "*.jpg",
        "*.svg",
        "*.json",
        "*.json5",
        "*.sample",
    ]

    @property
    def all_ignore_patterns(self) -> List[str]:
        """获取所有忽略模式的合并列表"""
        return (
            self.hidden_directories +
            self.ignore_directories +
            self.ignore_test_patterns +
            self.ignore_vcs_ide +
            self.ignore_logs_coverage +
            self.ignore_cache_temp +
            self.ignore_media_data
        )


class AppConfig(BaseModel):
    """应用配置"""
    repo_path: str
    persist_path: str = "./temp"
    host: str = "0.0.0.0"
    port: int = 5001
    log_level: str = "INFO"
    
    # 索引名称
    repo_hash_name: str = "repo_hash"
    bm25_index_name: str = "bm25_index_pro"
    embedding_index_name: str = "embedding_index_pro"
    graph_index_name: str = "graph_index_pro"
    
    # 支持的文件扩展名
    file_extensions: List[str] = [".py", ".js", ".ts", ".ets"]
    
    # 支持的编程语言
    supported_languages: List[str] = ["python", "java", "typescript", "javascript", "arkts"]
    
    # 配置
    index_config: IndexConfig = IndexConfig()
    embedding_config: EmbeddingConfig
    llm_config: LLMConfig
    rerank_config: RerankConfig = RerankConfig()
    ignore_config: IgnoreConfig = IgnoreConfig()

    @property
    def repo_name(self) -> str:
        return os.path.basename(self.repo_path)

    @property
    def repo_hash_path(self) -> str:
        return os.path.join(self.persist_path, self.repo_hash_name, self.repo_name)

    @property
    def bm25_persist_path(self) -> str:
        return os.path.join(self.persist_path, self.bm25_index_name, self.repo_name)
    
    @property
    def embedding_persist_path(self) -> str:
        return os.path.join(self.persist_path, self.embedding_index_name, self.repo_name)
    
    @property
    def graph_persist_path(self) -> str:
        return os.path.join(self.persist_path, self.graph_index_name, f"{self.repo_name}.pkl")


def create_config() -> AppConfig:
    """创建配置 - 支持命令行参数和环境变量，优先级：命令行 > 环境变量"""
    parser = argparse.ArgumentParser(description="CodebaseQA 服务")
    
    # 仓库路径参数（可以从环境变量获取）
    parser.add_argument("--repo-path", help="代码仓库路径（也可通过REPO_PATH环境变量设置）")
    
    # 可选参数
    parser.add_argument("--persist-path", help="索引持久化路径")
    parser.add_argument("--host", help="服务器主机地址")
    parser.add_argument("--port", type=int, help="服务器端口")
    parser.add_argument("--log-level", help="日志级别")
    
    # 索引配置
    parser.add_argument("--chunk-size", type=int, help="代码块大小")
    parser.add_argument("--max-chunk-size", type=int, help="最大代码块大小")
    parser.add_argument("--refresh-interval", type=int, help="索引刷新间隔(秒)")
    parser.add_argument("--enable-auto-refresh", type=bool, help="是否启用自动刷新")
    parser.add_argument("--enable-code-insights", action="store_true", help="是否启用code insights生成")
    
    # 模型配置
    parser.add_argument("--embedding-api-base", help="Embedding API基础URL")
    parser.add_argument("--embedding-api-key", help="Embedding API密钥")
    parser.add_argument("--embedding-model-name", help="Embedding模型名称")
    parser.add_argument("--llm-api-base", help="LLM API基础URL")
    parser.add_argument("--llm-api-key", help="LLM API密钥")
    parser.add_argument("--llm-model-name", help="LLM模型名称")
    parser.add_argument("--rerank-model-name", help="Rerank模型名称")
    parser.add_argument("--rerank-api-base", help="Rerank API基础URL")
    parser.add_argument("--rerank-api-key", help="Rerank API密钥")

    # 文件扩展名
    parser.add_argument("--file-extensions", nargs="+", help="支持的文件扩展名")
    parser.add_argument("--supported-languages", nargs="+", help="支持的编程语言")
    
    args = parser.parse_args()
    # args.repo_path = "/Users/<USER>/ide_dev/repo/CodebaseQA/example_repos/TouristParkDemo"
    # args.embedding_api_key = "sk-ravccrfhdceccblziftcgyzodzjbftdzrozbofbxrhayptnd"
    # args.llm_api_key = "sk-ravccrfhdceccblziftcgyzodzjbftdzrozbofbxrhayptnd"
    
    # 获取repo_path：命令行参数 > 环境变量
    repo_path = args.repo_path or os.environ.get("REPO_PATH")
    if not repo_path:
        raise ValueError("代码仓库路径未指定。请使用 --repo-path 参数或设置 REPO_PATH 环境变量")
    
    # 验证repo_path
    if not os.path.exists(repo_path):
        raise ValueError(f"仓库路径不存在: {repo_path}")
    
    # 创建配置
    config_dict = {
        "repo_path": os.path.abspath(repo_path),
    }

    # 只有在命令行参数或环境变量提供时才覆盖默认值
    if args.persist_path:
        config_dict["persist_path"] = args.persist_path
    if args.host:
        config_dict["host"] = args.host
    if args.port:
        config_dict["port"] = args.port
    if args.log_level:
        config_dict["log_level"] = args.log_level
    
    # 索引配置 - 只有在命令行参数或环境变量提供时才覆盖默认值
    index_config = {}
    if args.chunk_size:
        index_config["chunk_size"] = args.chunk_size
    if args.max_chunk_size:
        index_config["max_chunk_size"] = args.max_chunk_size
    if args.refresh_interval:
        index_config["refresh_interval"] = args.refresh_interval
    if args.enable_auto_refresh is not None:
        index_config["enable_auto_refresh"] = args.enable_auto_refresh
    if args.enable_code_insights:
        index_config["enable_code_insights"] = args.enable_code_insights

    config_dict["index_config"] = IndexConfig(**index_config)
    
    # Embedding配置 - 优先级：命令行参数 > 环境变量 > 默认值
    embedding_config = {}
    if args.embedding_api_key or os.environ.get("EMBEDDING_API_KEY"):
        embedding_config["api_key"] = args.embedding_api_key or os.environ.get("EMBEDDING_API_KEY")
    if args.embedding_api_base or os.environ.get("EMBEDDING_API_BASE"):
        embedding_config["api_base"] = args.embedding_api_base or os.environ.get("EMBEDDING_API_BASE")
    if args.embedding_model_name or os.environ.get("EMBEDDING_MODEL_NAME"):
        embedding_config["model_name"] = args.embedding_model_name or os.environ.get("EMBEDDING_MODEL_NAME")

    config_dict["embedding_config"] = EmbeddingConfig(**embedding_config)
    
    # LLM配置 - 优先级：命令行参数 > 环境变量 > 默认值
    llm_config = {}
    if args.llm_api_key or os.environ.get("LLM_API_KEY"):
        llm_config["api_key"] = args.llm_api_key or os.environ.get("LLM_API_KEY")
    if args.llm_api_base or os.environ.get("LLM_API_BASE"):
        llm_config["api_base"] = args.llm_api_base or os.environ.get("LLM_API_BASE")
    if args.llm_model_name or os.environ.get("LLM_MODEL_NAME"):
        llm_config["model_name"] = args.llm_model_name or os.environ.get("LLM_MODEL_NAME")

    config_dict["llm_config"] = LLMConfig(**llm_config)

    # Rerank配置 - 优先级：命令行参数 > 环境变量 > 默认值
    rerank_config = {}
    if args.rerank_model_name or os.environ.get("RERANK_MODEL_NAME"):
        rerank_config["model_name"] = args.rerank_model_name or os.environ.get("RERANK_MODEL_NAME")
    if args.rerank_api_base or os.environ.get("RERANK_API_BASE"):
        rerank_config["api_base"] = args.rerank_api_base or os.environ.get("RERANK_API_BASE")
    if args.rerank_api_key or os.environ.get("RERANK_API_KEY"):
        rerank_config["api_key"] = args.rerank_api_key or os.environ.get("RERANK_API_KEY")

    config_dict["rerank_config"] = RerankConfig(**rerank_config)

    # 文件扩展名
    if args.file_extensions:
        config_dict["file_extensions"] = args.file_extensions

    # 支持的语言
    if args.supported_languages:
        config_dict["supported_languages"] = args.supported_languages

    return AppConfig(**config_dict)


