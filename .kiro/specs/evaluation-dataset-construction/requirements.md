# 代码检索评测集构建需求文档

## 介绍

为CodeCompass代码检索工具构建一个全面的评测数据集，用于评估和优化检索系统的性能。该评测集将包含多种类型的查询场景，涵盖不同编程语言、查询复杂度和业务场景，以确保检索系统能够准确理解用户意图并返回相关的代码片段。

## 需求

### 需求 1：多样化查询场景构建

**用户故事：** 作为评测工程师，我希望能够构建涵盖各种真实使用场景的查询数据集，以全面评估检索系统的能力。

#### 验收标准

1. WHEN 构建查询场景时 THEN 系统应该支持至少5种不同类型的查询模式（实体查询、功能查询、业务逻辑查询、错误处理查询、架构查询）
2. WHEN 生成查询样本时 THEN 每种查询类型应该包含至少20个不同的查询示例
3. WHEN 设计查询时 THEN 应该涵盖中英文+代码混合查询、中文查询、英文查询、中英文查询、中文+代码查询、英文+代码查询、中文+英文+代码查询等语言模式
4. WHEN 创建查询时 THEN 应该包含简单查询（1-3个关键词）、中等复杂度查询（4-8个关键词）、复杂查询（8+个关键词）
5. WHEN 构建场景时 THEN 应该基于真实的开发者使用模式和常见问题类型

### 需求 2：标准答案标注系统

**用户故事：** 作为评测工程师，我希望为每个查询建立准确的标准答案，以便客观评估检索结果的质量。

#### 验收标准

1. WHEN 标注标准答案时 THEN 每个查询应该有1-5个相关的代码片段作为正确答案
2. WHEN 建立答案时 THEN 应该按照相关性等级（高度相关、中度相关、低度相关）对答案进行分级
3. WHEN 标注过程中 THEN 应该记录答案选择的理由和上下文信息
4. WHEN 处理多答案情况时 THEN 系统应该支持多个正确答案的并存
5. WHEN 验证答案质量时 THEN 应该通过多人标注和一致性检查确保答案准确性

### 需求 3：自动化数据生成

**用户故事：** 作为开发者，我希望能够自动化生成部分评测数据，以提高数据集构建的效率。

#### 验收标准

1. WHEN 分析现有代码库时 THEN 系统应该能够自动提取函数名、类名、文件名等实体信息
2. WHEN 生成查询模板时 THEN 系统应该基于代码结构自动生成基础查询模板
3. WHEN 扩展查询时 THEN 系统应该能够基于同义词库和业务词典自动生成查询变体
4. WHEN 处理多语言时 THEN 系统应该支持Python、TypeScript、JavaScript、ArkTS等语言的特定查询生成
5. WHEN 生成负样本时 THEN 系统应该能够生成与正确答案无关的干扰项

### 需求 4：评测指标计算

**用户故事：** 作为评测工程师，我希望能够使用标准的信息检索指标来量化评估检索系统的性能。

#### 验收标准

1. WHEN 计算评测指标时 THEN 系统应该支持Precision@K、Recall@K、F1@K等标准指标
2. WHEN 评估排序质量时 THEN 系统应该计算NDCG（归一化折损累积增益）指标
3. WHEN 分析检索效果时 THEN 系统应该提供MRR（平均倒数排名）指标
4. WHEN 评估整体性能时 THEN 系统应该计算MAP（平均精度均值）指标
5. WHEN 生成报告时 THEN 系统应该按查询类型、语言类型、复杂度等维度分别统计指标

### 需求 5：基准数据集管理

**用户故事：** 作为研发团队，我希望能够维护和版本化管理评测数据集，以支持持续的系统优化。

#### 验收标准

1. WHEN 存储数据集时 THEN 系统应该使用结构化格式（JSON/YAML）存储查询和答案
2. WHEN 管理版本时 THEN 数据集应该支持版本控制和变更追踪
3. WHEN 扩展数据集时 THEN 系统应该支持增量添加新的查询和答案
4. WHEN 质量控制时 THEN 系统应该提供数据集质量检查和统计功能
5. WHEN 分享数据集时 THEN 系统应该支持数据集的导出和导入功能

### 需求 6：多代码库适配

**用户故事：** 作为评测工程师，我希望评测框架能够适配不同类型和规模的代码库，以验证系统的通用性。

#### 验收标准

1. WHEN 处理不同规模代码库时 THEN 系统应该支持小型（<1000文件）、中型（1000-10000文件）、大型（>10000文件）代码库
2. WHEN 适配不同语言时 THEN 系统应该支持多语言混合的代码库评测
3. WHEN 处理不同领域时 THEN 系统应该涵盖Web开发、移动开发、系统软件、数据科学等不同领域的代码库
4. WHEN 构建查询时 THEN 系统应该根据代码库特点生成领域特定的查询
5. WHEN 评估性能时 THEN 系统应该提供跨代码库的性能对比分析

### 需求 7：人工评估接口

**用户故事：** 作为评测专家，我希望有便捷的界面来进行人工评估和标注，以补充自动化评测的不足。

#### 验收标准

1. WHEN 进行人工评估时 THEN 系统应该提供直观的Web界面展示查询和检索结果
2. WHEN 标注相关性时 THEN 界面应该支持快速的相关性等级标注（1-5分）
3. WHEN 批量处理时 THEN 系统应该支持批量标注和快捷键操作
4. WHEN 质量控制时 THEN 系统应该支持多人标注和一致性分析
5. WHEN 导出结果时 THEN 系统应该能够导出人工评估结果用于进一步分析